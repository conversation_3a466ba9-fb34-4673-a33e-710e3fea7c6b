# 火山引擎群聊API技术文档与实现方案

## 1. 官方文档核心信息提取

### 1.1 应用(Bot) API文档 (1526787) 核心要点

**群聊配置参数结构：**
```json
{
  "metadata": {
    "group_chat_config": {
      "user_name": "用户角色名",
      "description": "场景描述", 
      "characters": [
        {
          "name": "群聊角色名称",
          "system_prompt": "角色系统提示词",
          "model_desc": {
            "endpoint_id": "ep-xxxxxxxxx"
          }
        }
      ]
    }
  },
  "target_character_name": "指定发言角色名（可选）"
}
```

**关键参数说明：**
- `user_name`: 用户在群聊中的角色名称
- `description`: 当前场景或对话背景描述
- `characters`: 群聊中的AI角色列表
- `characters.name`: 角色名称
- `characters.system_prompt`: 角色的系统提示词，定义角色性格和行为
- `characters.model_desc.endpoint_id`: 角色使用的模型端点ID
- `target_character_name`: 可选参数，指定特定角色发言

### 1.2 角色扮演场景提示词指南 (1256348) 核心要点

**群聊实现方案对比：**

**方案1：接口实现（官方推荐）**
- 通过Bot接口和character版本模型
- 在应用广场创建群聊应用，获取Bot ID
- 通过API调用实现群聊功能

**方案2：PE版本实现方式（不推荐）**
- 多Bot群聊的算法实现方式
- 官方不推荐使用

**群聊接口调用的两种方式：**

1. **指定发言人模式**：
   - 配置`target_character_name`字段
   - 指定一个角色根据上下文来回答
   - 用户可以控制哪个角色发言

2. **自动选择模式**：
   - 请求中不配置`target_character_name`
   - 模型自动选择合适的角色发言
   - 系统根据上下文智能决定发言者

## 2. 技术实现详细方案

### 2.1 基础API调用结构

**完整的API请求示例：**
```python
import requests

def call_volcengine_group_chat(bot_id, messages, group_config, target_character=None):
    """
    调用火山引擎群聊API
    
    Args:
        bot_id: 应用广场创建的Bot ID
        messages: 对话历史消息列表
        group_config: 群聊配置
        target_character: 指定发言角色（可选）
    """
    
    url = "https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "bot_id": bot_id,
        "messages": messages,
        "stream": False,  # 或True启用流式响应
        "metadata": {
            "group_chat_config": {
                "user_name": group_config["user_name"],
                "description": group_config["description"],
                "characters": group_config["characters"]
            }
        }
    }
    
    # 如果指定了发言角色
    if target_character:
        payload["target_character_name"] = target_character
    
    response = requests.post(url, headers=headers, json=payload)
    return response.json()
```

### 2.2 角色配置管理系统

**角色配置数据结构：**
```python
class CharacterConfig:
    def __init__(self, name, system_prompt, endpoint_id=None, status="active"):
        self.name = name
        self.system_prompt = system_prompt
        self.endpoint_id = endpoint_id
        self.status = status  # active, inactive, absent
        self.scene_context = ""
        
    def to_api_format(self):
        """转换为API调用格式"""
        config = {
            "name": self.name,
            "system_prompt": self.system_prompt
        }
        
        if self.endpoint_id:
            config["model_desc"] = {
                "endpoint_id": self.endpoint_id
            }
            
        return config

# 角色管理器
class CharacterManager:
    def __init__(self):
        self.characters = {}
        self.active_characters = []
        self.current_scene = ""
        
    def add_character(self, character_config):
        """添加角色"""
        self.characters[character_config.name] = character_config
        
    def set_active_characters(self, character_names):
        """设置当前在场角色"""
        self.active_characters = [
            name for name in character_names 
            if name in self.characters
        ]
        
    def get_active_characters_config(self):
        """获取在场角色的API配置"""
        return [
            self.characters[name].to_api_format()
            for name in self.active_characters
        ]
        
    def is_character_active(self, character_name):
        """检查角色是否在场"""
        return character_name in self.active_characters
```

### 2.3 场景驱动的角色控制

**场景管理系统：**
```python
class SceneManager:
    def __init__(self):
        self.scenes = {}
        self.current_scene_id = None
        self.character_manager = CharacterManager()
        
    def define_scene(self, scene_id, scene_config):
        """定义场景"""
        self.scenes[scene_id] = {
            "name": scene_config["name"],
            "description": scene_config["description"],
            "default_characters": scene_config["default_characters"],
            "available_characters": scene_config["available_characters"],
            "background": scene_config.get("background", ""),
            "transitions": scene_config.get("transitions", {})
        }
        
    def switch_to_scene(self, scene_id):
        """切换场景"""
        if scene_id not in self.scenes:
            raise ValueError(f"Scene {scene_id} not found")
            
        self.current_scene_id = scene_id
        scene = self.scenes[scene_id]
        
        # 更新在场角色
        self.character_manager.set_active_characters(
            scene["default_characters"]
        )
        
        return {
            "scene_name": scene["name"],
            "description": scene["description"],
            "active_characters": scene["default_characters"],
            "available_transitions": scene["transitions"]
        }
        
    def get_current_scene_description(self):
        """获取当前场景描述"""
        if not self.current_scene_id:
            return "未设置场景"
            
        scene = self.scenes[self.current_scene_id]
        active_chars = ", ".join(self.character_manager.active_characters)
        
        return f"{scene['description']} 在场角色：{active_chars}"
```

### 2.4 智能对话控制系统

**对话控制器：**
```python
class DialogueController:
    def __init__(self, bot_id, character_manager, scene_manager):
        self.bot_id = bot_id
        self.character_manager = character_manager
        self.scene_manager = scene_manager
        self.dialogue_history = []
        
    def send_user_message(self, user_message, user_name="用户"):
        """发送用户消息"""
        message = {
            "role": "user",
            "content": user_message
        }
        self.dialogue_history.append(message)
        
    def trigger_character_response(self, character_name=None):
        """触发角色回复"""
        
        # 验证角色是否在场
        if character_name and not self.character_manager.is_character_active(character_name):
            return {
                "error": "character_not_active",
                "message": f"{character_name}不在当前场景中，无法发言"
            }
        
        # 构建群聊配置
        group_config = {
            "user_name": "用户",
            "description": self.scene_manager.get_current_scene_description(),
            "characters": self.character_manager.get_active_characters_config()
        }
        
        # 调用API
        try:
            response = call_volcengine_group_chat(
                bot_id=self.bot_id,
                messages=self.dialogue_history,
                group_config=group_config,
                target_character=character_name
            )
            
            if "choices" in response and response["choices"]:
                ai_message = response["choices"][0]["message"]["content"]
                speaker = character_name or self._extract_speaker_from_response(ai_message)
                
                # 添加到对话历史
                self.dialogue_history.append({
                    "role": "assistant", 
                    "content": ai_message
                })
                
                return {
                    "success": True,
                    "speaker": speaker,
                    "message": ai_message,
                    "response_data": response
                }
            else:
                return {
                    "error": "api_error",
                    "message": "API调用失败",
                    "response": response
                }
                
        except Exception as e:
            return {
                "error": "exception",
                "message": str(e)
            }
            
    def _extract_speaker_from_response(self, response_text):
        """从回复中提取发言者（当未指定target_character时）"""
        # 这里需要根据实际API返回格式来解析
        # 火山引擎可能在回复中包含角色名信息
        for char_name in self.character_manager.active_characters:
            if char_name in response_text[:20]:  # 检查开头是否包含角色名
                return char_name
        return "未知角色"

### 2.5 Ren'Py集成实现

**Ren'Py游戏集成：**
```python
# Ren'Py中的集成代码
init python:
    # 初始化系统组件
    character_manager = CharacterManager()
    scene_manager = SceneManager()
    dialogue_controller = DialogueController(
        bot_id="your_bot_id",
        character_manager=character_manager,
        scene_manager=scene_manager
    )

    # 定义角色
    character_manager.add_character(CharacterConfig(
        name="小雪",
        system_prompt="你是小雪，一个温柔善良的高中生，性格内向但很关心朋友。你说话温和，经常关心别人的感受。",
        endpoint_id="ep-20250703110544-ht42m"
    ))

    character_manager.add_character(CharacterConfig(
        name="阿明",
        system_prompt="你是阿明，一个活泼开朗的少年，总是充满正能量。你幽默风趣，喜欢开玩笑，但也很关心朋友。",
        endpoint_id="ep-20250703110544-ht42m"
    ))

    # 定义场景
    scene_manager.define_scene("classroom", {
        "name": "教室",
        "description": "安静的教室，阳光透过窗户洒进来。",
        "default_characters": ["小雪"],
        "available_characters": ["小雪", "阿明", "老师"],
        "transitions": {
            "library": "去图书馆",
            "cafeteria": "去食堂"
        }
    })

    scene_manager.define_scene("library", {
        "name": "图书馆",
        "description": "安静的图书馆，书香阵阵。",
        "default_characters": ["阿明"],
        "available_characters": ["阿明", "图书管理员"],
        "transitions": {
            "classroom": "回教室"
        }
    })

    # 初始化场景
    scene_manager.switch_to_scene("classroom")

# 角色控制界面
screen character_control_panel:
    vbox:
        text "角色对话" size 20

        # 显示在场角色
        for char_name in character_manager.active_characters:
            textbutton f"让{char_name}说话" action Function(trigger_ai_speak, char_name):
                text_color "#00AA00"

        # 显示不在场角色（禁用状态）
        for char_name in character_manager.characters:
            if char_name not in character_manager.active_characters:
                textbutton f"{char_name}(不在场)" action NullAction():
                    text_color "#888888"

        null height 20

        text "场景转换" size 16
        if scene_manager.current_scene_id in scene_manager.scenes:
            for target_scene, description in scene_manager.scenes[scene_manager.current_scene_id]["transitions"].items():
                textbutton description action Function(change_scene, target_scene)

# 游戏逻辑函数
init python:
    def trigger_ai_speak(character_name):
        """触发AI角色发言"""
        result = dialogue_controller.trigger_character_response(character_name)

        if result.get("success"):
            # 显示AI回复
            speaker = result["speaker"]
            message = result["message"]

            # 在Ren'Py中显示消息
            renpy.say(None, f"{speaker}: {message}")
        else:
            # 显示错误信息
            renpy.notify(result.get("message", "发言失败"))

    def change_scene(target_scene_id):
        """切换场景"""
        try:
            scene_info = scene_manager.switch_to_scene(target_scene_id)

            # 显示场景转换
            renpy.say(None, f"你来到了{scene_info['scene_name']}。{scene_info['description']}")

            # 显示新在场角色
            active_chars = "、".join(scene_info['active_characters'])
            renpy.say(None, f"在场角色：{active_chars}")

            # 刷新界面
            renpy.restart_interaction()

        except Exception as e:
            renpy.notify(f"场景切换失败：{str(e)}")

    def send_user_message(message):
        """发送用户消息"""
        dialogue_controller.send_user_message(message)
        renpy.say(None, f"你: {message}")
```

### 2.6 高级功能实现

**流式响应处理：**
```python
import json
import requests

def call_volcengine_stream(bot_id, messages, group_config, target_character=None):
    """流式调用火山引擎群聊API"""

    payload = {
        "bot_id": bot_id,
        "messages": messages,
        "stream": True,  # 启用流式响应
        "metadata": {
            "group_chat_config": group_config
        }
    }

    if target_character:
        payload["target_character_name"] = target_character

    response = requests.post(
        "https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions",
        headers={"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"},
        json=payload,
        stream=True
    )

    # 处理流式响应
    full_response = ""
    for line in response.iter_lines():
        if line:
            line_text = line.decode('utf-8')
            if line_text.startswith('data: '):
                data_text = line_text[6:]  # 移除'data: '前缀
                if data_text != '[DONE]':
                    try:
                        data = json.loads(data_text)
                        if 'choices' in data and data['choices']:
                            delta = data['choices'][0].get('delta', {})
                            if 'content' in delta:
                                content = delta['content']
                                full_response += content
                                yield content  # 实时返回增量内容
                    except json.JSONDecodeError:
                        continue

    return full_response
```

**错误处理和重试机制：**
```python
import time
import random

class APIRetryHandler:
    def __init__(self, max_retries=3, base_delay=1):
        self.max_retries = max_retries
        self.base_delay = base_delay

    def call_with_retry(self, api_func, *args, **kwargs):
        """带重试的API调用"""
        last_error = None

        for attempt in range(self.max_retries + 1):
            try:
                return api_func(*args, **kwargs)
            except Exception as e:
                last_error = e

                if attempt < self.max_retries:
                    # 指数退避 + 随机抖动
                    delay = self.base_delay * (2 ** attempt) + random.uniform(0, 1)
                    time.sleep(delay)
                    continue
                else:
                    # 最后一次尝试失败
                    break

        # 所有重试都失败了
        return {
            "error": "max_retries_exceeded",
            "message": f"API调用失败，已重试{self.max_retries}次",
            "last_error": str(last_error)
        }

# 使用示例
retry_handler = APIRetryHandler(max_retries=3, base_delay=1)

def robust_character_response(character_name):
    """带重试机制的角色回复"""
    return retry_handler.call_with_retry(
        dialogue_controller.trigger_character_response,
        character_name
    )
```

## 3. 完整的技术实现方案

### 3.1 系统架构设计

```
┌─────────────────────────────────────────────────────────┐
│                 Ren'Py 游戏客户端                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │ 角色控制UI  │ │ 场景管理UI  │ │   对话显示UI        │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                 本地游戏逻辑层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │角色管理器   │ │场景管理器   │ │  对话控制器         │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                            │ HTTPS API调用
┌─────────────────────────────────────────────────────────┐
│                火山引擎群聊API                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │群聊应用Bot  │ │Character模型│ │   API网关           │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 3.2 关键技术特性

**1. 双模式角色控制：**
- **指定模式**：用户点击按钮指定角色发言
- **自动模式**：AI根据上下文自动选择发言角色

**2. 场景驱动的角色管理：**
- 角色状态与场景绑定
- 场景切换自动更新在场角色
- UI界面实时反映角色可用性

**3. 智能对话流控制：**
- 基于场景上下文的提示词生成
- 角色一致性保持机制
- 对话历史管理和压缩

**4. 错误处理和用户体验：**
- 优雅的错误提示
- API调用重试机制
- 流式响应支持（可选）

### 3.3 实施步骤

**第一阶段：基础集成（1-2周）**
1. 在火山引擎应用广场创建群聊应用
2. 获取Bot ID和API密钥
3. 实现基础的API调用功能
4. 创建简单的角色配置

**第二阶段：核心功能（2-3周）**
1. 实现角色管理器和场景管理器
2. 开发对话控制器
3. 集成Ren'Py界面控制
4. 实现角色状态验证

**第三阶段：高级功能（2-3周）**
1. 添加流式响应支持
2. 实现错误处理和重试机制
3. 优化用户体验和界面
4. 添加场景转换动画

**第四阶段：测试优化（1-2周）**
1. 全面测试各种场景
2. 性能优化和错误修复
3. 用户体验调优
4. 文档编写和部署准备

## 4. 角色出场控制的核心机制

### 4.1 基于火山引擎API的角色控制策略

**核心原理：**
1. **API层面控制**：通过`group_chat_config.characters`只传入在场角色
2. **UI层面控制**：禁用不在场角色的操作按钮
3. **逻辑层面验证**：调用前验证角色是否在当前场景

**实现细节：**
```python
def ensure_character_availability(character_name, scene_manager):
    """确保角色可用性检查"""
    if not scene_manager.character_manager.is_character_active(character_name):
        return {
            "available": False,
            "reason": f"{character_name}不在当前场景中",
            "suggestion": f"你可以去{get_character_location(character_name)}找{character_name}"
        }

    return {"available": True}

def get_character_location(character_name):
    """获取角色当前位置"""
    for scene_id, scene_config in scene_manager.scenes.items():
        if character_name in scene_config["default_characters"]:
            return scene_config["name"]
    return "未知位置"
```

### 4.2 用户体验优化

**智能提示系统：**
- 当用户尝试与不在场角色对话时，提供合理的解释
- 建议用户如何找到该角色（场景转换）
- 显示角色当前可能的位置

**视觉反馈机制：**
- 在场角色：绿色按钮，可点击
- 不在场角色：灰色按钮，显示"不在场"状态
- 场景转换：提供明确的转换选项

这个技术方案基于火山引擎官方文档的深度分析，提供了完整的角色出场控制机制，确保游戏逻辑的一致性和用户体验的流畅性。
```

# 多AI智能体游戏角色档案系统技术方案

## 项目概述

本文档详细设计了基于火山引擎群聊API的多用户多AI智能体游戏角色档案系统，支持每个用户独立的游戏体验、角色关系发展和剧情进展。

### 核心特性
- **多用户数据隔离**：每个用户拥有独立的游戏世界
- **动态角色发展**：角色会根据互动历史发生性格和关系变化
- **智能好感度系统**：基于用户行为自动调整角色对用户的态度
- **长期记忆管理**：解决Token限制，实现真正的角色记忆
- **个性化AI回复**：角色档案直接影响AI的回复风格和内容

## 1. 系统架构设计

### 1.1 整体架构

```
┌─────────────────────────────────────────────────────────┐
│                 Ren'Py 游戏客户端                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │ 用户界面    │ │ 角色交互    │ │   档案管理          │ │
│  │ 模块        │ │ 模块        │ │   模块              │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                            │ HTTP API + 本地缓存
┌─────────────────────────────────────────────────────────┐
│                 档案管理服务层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │用户档案管理 │ │角色档案管理 │ │  好感度系统         │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │记忆管理系统 │ │剧情进度管理 │ │  数据同步服务       │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                            │ SQL + Redis
┌─────────────────────────────────────────────────────────┐
│                   数据存储层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │   MySQL     │ │    Redis    │ │   文件存储          │ │
│  │(主数据库)   │ │(缓存/会话)  │ │  (图片/音频)        │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 1.2 技术选型

**数据库选型：MySQL + Redis**
- **MySQL**：主数据库，广泛使用，生态成熟，JSON字段支持（5.7+）
- **Redis**：缓存层，提供高速数据访问和会话管理
- **本地SQLite**：Ren'Py客户端缓存，支持离线使用

**MySQL优势：**
- 运维简单，管理工具丰富
- 社区活跃，文档完善
- 性能优秀，适合中小型项目
- 成本相对较低

**开发框架：**
- **后端**：Flask/FastAPI + SQLAlchemy + PyMySQL
- **前端**：Ren'Py游戏引擎
- **部署**：Docker + Nginx

**MySQL Python依赖：**
```bash
pip install pymysql sqlalchemy flask-sqlalchemy
# 或者使用 mysqlclient（性能更好但安装复杂）
# pip install mysqlclient
```

## 2. 数据库设计

### 2.1 核心数据表结构

#### 用户相关表

```sql
-- 用户基础信息表
CREATE TABLE users (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    display_name VARCHAR(100),
    avatar_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- 用户游戏档案表
CREATE TABLE user_profiles (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id CHAR(36) NOT NULL,
    game_name VARCHAR(100), -- 用户在游戏中的名字
    age INTEGER,
    gender VARCHAR(20),
    personality_traits JSON, -- 用户性格特征
    preferences JSON, -- 用户偏好设置
    game_settings JSON, -- 游戏设置
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 用户游戏进度表
CREATE TABLE user_game_progress (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id CHAR(36) NOT NULL,
    current_chapter VARCHAR(100),
    current_scene VARCHAR(100),
    story_flags JSON, -- 剧情标记
    unlocked_content JSON, -- 已解锁内容
    achievements JSON, -- 成就记录
    play_time_minutes INTEGER DEFAULT 0,
    last_save_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 角色相关表

```sql
-- 角色基础信息表（模板）
CREATE TABLE character_templates (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    character_name VARCHAR(100) NOT NULL,
    character_id VARCHAR(50) UNIQUE NOT NULL, -- 角色标识符
    base_info JSON NOT NULL, -- 基础信息
    appearance JSON NOT NULL, -- 外貌描述
    personality JSON NOT NULL, -- 性格特征
    background_story TEXT, -- 背景故事
    system_prompt TEXT NOT NULL, -- AI系统提示词
    voice_style JSON, -- 语音风格
    default_avatar VARCHAR(500), -- 默认头像
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色实例表（每个用户的角色副本）
CREATE TABLE user_characters (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id CHAR(36) NOT NULL,
    character_template_id CHAR(36) NOT NULL,
    character_name VARCHAR(100) NOT NULL,
    current_info JSON NOT NULL, -- 当前状态信息
    appearance_state JSON, -- 当前外貌状态
    personality_development JSON, -- 性格发展
    custom_background TEXT, -- 个性化背景
    current_avatar VARCHAR(500), -- 当前头像
    is_unlocked BOOLEAN DEFAULT FALSE, -- 是否已解锁
    unlock_condition VARCHAR(200), -- 解锁条件
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE(user_id, character_template_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (character_template_id) REFERENCES character_templates(id)
);

-- 角色状态表
CREATE TABLE character_states (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_character_id CHAR(36) NOT NULL,
    current_emotion VARCHAR(50), -- 当前情绪
    emotion_intensity INTEGER DEFAULT 5, -- 情绪强度 1-10
    health_status VARCHAR(50) DEFAULT 'normal', -- 健康状态
    activity_status VARCHAR(50) DEFAULT 'available', -- 活动状态
    location VARCHAR(100), -- 当前位置
    special_states JSON, -- 特殊状态
    state_duration INTEGER DEFAULT 0, -- 状态持续时间（分钟）
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_character_id) REFERENCES user_characters(id) ON DELETE CASCADE
);
```

#### 好感度系统表

```sql
-- 好感度记录表
CREATE TABLE affection_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    character_id UUID REFERENCES user_characters(id) ON DELETE CASCADE,
    current_level INTEGER DEFAULT 0, -- 当前好感度数值
    max_level INTEGER DEFAULT 100, -- 最大好感度
    affection_stage VARCHAR(50) DEFAULT 'stranger', -- 好感度阶段
    stage_progress DECIMAL(5,2) DEFAULT 0.00, -- 阶段进度百分比
    last_interaction_at TIMESTAMP,
    total_interactions INTEGER DEFAULT 0,
    positive_interactions INTEGER DEFAULT 0,
    negative_interactions INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, character_id)
);

-- 好感度变化历史表
CREATE TABLE affection_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    affection_record_id UUID REFERENCES affection_records(id) ON DELETE CASCADE,
    change_amount INTEGER NOT NULL, -- 变化数值（可为负）
    change_reason VARCHAR(200), -- 变化原因
    trigger_event VARCHAR(100), -- 触发事件
    previous_level INTEGER, -- 变化前数值
    new_level INTEGER, -- 变化后数值
    context_data JSONB, -- 上下文数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 好感度等级配置表
CREATE TABLE affection_levels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    level_name VARCHAR(50) NOT NULL, -- 等级名称
    min_points INTEGER NOT NULL, -- 最小分数
    max_points INTEGER NOT NULL, -- 最大分数
    level_description TEXT, -- 等级描述
    unlock_features JSONB, -- 解锁功能
    special_dialogues JSONB, -- 特殊对话
    character_behavior JSONB, -- 角色行为变化
    display_order INTEGER NOT NULL,
    
    UNIQUE(level_name)
);
```

#### 记忆和对话表

```sql
-- 角色记忆表
CREATE TABLE character_memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_character_id UUID REFERENCES user_characters(id) ON DELETE CASCADE,
    memory_type VARCHAR(50) NOT NULL, -- 记忆类型
    memory_category VARCHAR(50), -- 记忆分类
    memory_content TEXT NOT NULL, -- 记忆内容
    importance_score INTEGER DEFAULT 5, -- 重要性评分 1-10
    emotional_impact INTEGER DEFAULT 0, -- 情感影响 -10到10
    associated_characters JSONB, -- 关联角色
    memory_tags JSONB, -- 记忆标签
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP, -- 记忆过期时间（可选）
    is_active BOOLEAN DEFAULT TRUE
);

-- 对话历史表
CREATE TABLE dialogue_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(100), -- 会话ID
    speaker_type VARCHAR(20) NOT NULL, -- 'user', 'character', 'system'
    speaker_id UUID, -- 角色ID（如果是角色发言）
    speaker_name VARCHAR(100),
    content TEXT NOT NULL,
    content_type VARCHAR(50) DEFAULT 'text', -- 内容类型
    emotion_tag VARCHAR(50), -- 情感标签
    importance_score INTEGER DEFAULT 1, -- 重要性评分
    token_count INTEGER, -- Token数量
    scene_context VARCHAR(200), -- 场景上下文
    metadata JSONB, -- 元数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 对话会话表
CREATE TABLE dialogue_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_name VARCHAR(200),
    scene_id VARCHAR(100),
    active_characters JSONB, -- 参与角色列表
    session_status VARCHAR(50) DEFAULT 'active',
    total_messages INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.2 MySQL特有配置

```sql
-- MySQL配置优化
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- 字符集设置（支持emoji等特殊字符）
ALTER DATABASE gamedb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 表字符集设置示例
ALTER TABLE users CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2.3 性能优化索引

```sql
-- 性能优化索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_user_characters_user_id ON user_characters(user_id);
CREATE INDEX idx_user_characters_template ON user_characters(character_template_id);
CREATE INDEX idx_affection_user_character ON affection_records(user_id, character_id);
CREATE INDEX idx_dialogue_user_session ON dialogue_history(user_id, session_id);
CREATE INDEX idx_dialogue_created_at ON dialogue_history(created_at DESC);
CREATE INDEX idx_character_memories_user_char ON character_memories(user_character_id);
CREATE INDEX idx_character_memories_importance ON character_memories(importance_score DESC);
CREATE INDEX idx_character_states_user_char ON character_states(user_character_id);

-- MySQL JSON字段索引（MySQL 5.7+）
CREATE INDEX idx_user_profiles_preferences ON user_profiles((CAST(preferences->'$.language' AS CHAR(10))));
CREATE INDEX idx_character_memories_tags ON character_memories((CAST(memory_tags->'$.category' AS CHAR(50))));
```

### 2.4 MySQL性能优化建议

**配置优化：**
```ini
# my.cnf 配置建议
[mysqld]
# 基础配置
default-storage-engine = InnoDB
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 性能优化
innodb_buffer_pool_size = 1G  # 根据服务器内存调整
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 连接优化
max_connections = 200
max_connect_errors = 1000
wait_timeout = 600
interactive_timeout = 600

# JSON优化
optimizer_switch = 'derived_merge=off'  # 对复杂JSON查询有帮助
```

## 3. 角色档案数据结构

### 3.1 角色基础信息JSON结构

```json
{
  "character_template": {
    "character_id": "xiaoxue_001",
    "character_name": "小雪",
    "base_info": {
      "age": 17,
      "gender": "female",
      "occupation": "高中生",
      "grade": "高二",
      "birthday": "03-15",
      "blood_type": "A",
      "height": "162cm",
      "hobbies": ["读书", "画画", "听音乐"],
      "skills": ["学习能力强", "绘画天赋", "温柔体贴"]
    },
    "appearance": {
      "hair": {
        "color": "黑色",
        "style": "长直发",
        "length": "及腰"
      },
      "eyes": {
        "color": "棕色",
        "shape": "杏眼",
        "expression": "温柔"
      },
      "body": {
        "build": "纤细",
        "skin": "白皙"
      },
      "clothing": {
        "default": "校服",
        "casual": "淡色连衣裙",
        "formal": "白色衬衫配百褶裙"
      },
      "accessories": ["发夹", "小包包"]
    },
    "personality": {
      "core_traits": ["温柔", "内向", "善良", "细心"],
      "secondary_traits": ["有点害羞", "责任感强", "喜欢帮助别人"],
      "speech_style": {
        "tone": "温和",
        "speed": "适中",
        "vocabulary": "文雅",
        "habits": ["说话前会思考", "喜欢用敬语"]
      },
      "emotional_tendencies": {
        "happiness": 7,
        "sadness": 3,
        "anger": 2,
        "fear": 4,
        "surprise": 6
      }
    },
    "background_story": "小雪是一个温柔善良的高中女生，从小成绩优秀，深受老师和同学喜爱。她有着细腻的心思和强烈的责任感，总是默默关心着身边的人。虽然性格内向，但对于重要的事情会勇敢表达自己的想法。",
    "system_prompt": "你是小雪，一个17岁的高中女生。你性格温柔内向，善良细心，说话温和有礼貌。你喜欢读书和画画，成绩优秀，深受大家喜爱。在对话中要体现出你的温柔和体贴，说话要符合高中生的语言习惯。"
  }
}
```

### 3.2 用户角色实例数据结构

```json
{
  "user_character_instance": {
    "user_id": "user_12345",
    "character_template_id": "xiaoxue_001",
    "current_info": {
      "relationship_status": "同学",
      "familiarity_level": "熟悉",
      "trust_level": 7,
      "shared_experiences": [
        "一起做过作业",
        "在图书馆偶遇过",
        "参加过同一个社团活动"
      ],
      "personal_secrets": [
        "知道用户喜欢看小说",
        "记得用户的生日"
      ]
    },
    "appearance_state": {
      "current_outfit": "校服",
      "mood_expression": "微笑",
      "special_accessories": ["粉色发夹"],
      "seasonal_changes": {
        "spring": "淡色外套",
        "summer": "短袖校服",
        "autumn": "薄毛衣",
        "winter": "厚外套"
      }
    },
    "personality_development": {
      "confidence_growth": 2,
      "openness_increase": 3,
      "trust_development": 5,
      "emotional_stability": 1,
      "learned_behaviors": [
        "更愿意主动找用户聊天",
        "会记住用户的喜好"
      ]
    }
  }
}
```

### 3.3 好感度系统配置

```json
{
  "affection_system": {
    "levels": [
      {
        "name": "陌生人",
        "min_points": 0,
        "max_points": 19,
        "description": "刚刚认识，还不太熟悉",
        "behavior_changes": {
          "speech_style": "礼貌但疏远",
          "interaction_frequency": "低",
          "topic_openness": "基础话题"
        },
        "available_actions": ["基础对话", "简单问候"]
      },
      {
        "name": "普通朋友",
        "min_points": 20,
        "max_points": 39,
        "description": "成为了普通朋友，会主动聊天",
        "behavior_changes": {
          "speech_style": "友好亲切",
          "interaction_frequency": "中等",
          "topic_openness": "日常生活话题"
        },
        "available_actions": ["日常聊天", "分享心情", "一起学习"]
      },
      {
        "name": "好朋友",
        "min_points": 40,
        "max_points": 59,
        "description": "关系很好的朋友，会分享秘密",
        "behavior_changes": {
          "speech_style": "亲密自然",
          "interaction_frequency": "高",
          "topic_openness": "个人话题"
        },
        "available_actions": ["深入交流", "分享秘密", "互相帮助", "一起活动"]
      },
      {
        "name": "挚友",
        "min_points": 60,
        "max_points": 79,
        "description": "非常信任的挚友关系",
        "behavior_changes": {
          "speech_style": "完全放松",
          "interaction_frequency": "很高",
          "topic_openness": "所有话题"
        },
        "available_actions": ["无话不谈", "情感支持", "特殊活动", "专属对话"]
      },
      {
        "name": "特殊关系",
        "min_points": 80,
        "max_points": 100,
        "description": "超越友谊的特殊关系",
        "behavior_changes": {
          "speech_style": "温柔体贴",
          "interaction_frequency": "最高",
          "topic_openness": "深层情感话题"
        },
        "available_actions": ["情感表达", "特殊剧情", "专属内容", "深度互动"]
      }
    ],
    "change_triggers": {
      "positive_actions": {
        "compliment": 2,
        "help_with_study": 3,
        "remember_important_date": 5,
        "emotional_support": 4,
        "gift_giving": 3,
        "quality_time": 2
      },
      "negative_actions": {
        "ignore_message": -1,
        "rude_comment": -3,
        "break_promise": -5,
        "hurt_feelings": -4,
        "inappropriate_behavior": -6
      },
      "special_events": {
        "birthday_celebration": 8,
        "help_in_crisis": 10,
        "share_deep_secret": 6,
        "defend_from_others": 7
      }
    }
  }
}
```

## 4. 系统集成方案

### 4.1 Ren'Py数据库集成

```python
# Ren'Py中的数据库连接管理
init python:
    import sqlite3
    import json
    import requests
    from datetime import datetime

    class GameDatabase:
        def __init__(self, db_path="game_data.db"):
            self.db_path = db_path
            self.connection = None
            self.init_database()

        def init_database(self):
            """初始化本地数据库"""
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row

            # 创建本地缓存表
            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS local_cache (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    expires_at INTEGER,
                    created_at INTEGER DEFAULT (strftime('%s', 'now'))
                )
            ''')

            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS sync_queue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    operation TEXT,
                    table_name TEXT,
                    data TEXT,
                    created_at INTEGER DEFAULT (strftime('%s', 'now')),
                    synced BOOLEAN DEFAULT FALSE
                )
            ''')

            self.connection.commit()

        def get_user_character(self, user_id, character_id):
            """获取用户角色信息"""
            # 先尝试从本地缓存获取
            cache_key = f"user_character_{user_id}_{character_id}"
            cached_data = self.get_cache(cache_key)

            if cached_data:
                return json.loads(cached_data)

            # 从远程API获取
            try:
                response = requests.get(
                    f"{API_BASE_URL}/users/{user_id}/characters/{character_id}",
                    headers=self.get_auth_headers(),
                    timeout=5
                )

                if response.status_code == 200:
                    character_data = response.json()
                    # 缓存到本地
                    self.set_cache(cache_key, json.dumps(character_data), 300)
                    return character_data

            except requests.RequestException:
                # 网络错误，使用本地备份数据
                return self.get_local_backup(cache_key)

        def update_affection(self, user_id, character_id, change_amount, reason):
            """更新好感度"""
            # 添加到同步队列
            sync_data = {
                "user_id": user_id,
                "character_id": character_id,
                "change_amount": change_amount,
                "reason": reason,
                "timestamp": datetime.now().isoformat()
            }

            self.add_to_sync_queue("UPDATE", "affection_records", json.dumps(sync_data))

            # 立即尝试同步
            self.sync_to_remote()

    # 全局数据库实例
    game_db = GameDatabase()
```

### 4.2 角色档案与AI集成

```python
# 角色档案与火山引擎API集成
init python:
    class CharacterProfileManager:
        def __init__(self, db_manager):
            self.db = db_manager
            self.current_user_id = None

        def set_current_user(self, user_id):
            """设置当前用户"""
            self.current_user_id = user_id

        def build_character_context(self, character_id, recent_messages):
            """构建角色上下文"""
            # 获取角色档案
            character_data = self.db.get_user_character(self.current_user_id, character_id)

            if not character_data:
                return None

            # 获取好感度信息
            affection_data = self.db.get_affection_record(self.current_user_id, character_id)

            # 获取角色记忆
            memories = self.db.get_character_memories(self.current_user_id, character_id, limit=10)

            # 构建系统提示词
            system_prompt = self.build_system_prompt(character_data, affection_data, memories)

            # 构建上下文消息
            context_messages = []

            # 添加系统提示词
            context_messages.append({
                "role": "system",
                "content": system_prompt
            })

            # 添加记忆摘要
            if memories:
                memory_summary = self.create_memory_summary(memories)
                context_messages.append({
                    "role": "system",
                    "content": f"重要记忆：{memory_summary}"
                })

            # 添加最近对话
            context_messages.extend(recent_messages[-10:])  # 最近10轮对话

            return context_messages

        def build_system_prompt(self, character_data, affection_data, memories):
            """构建系统提示词"""
            base_prompt = character_data["system_prompt"]

            # 添加好感度相关的行为调整
            affection_level = affection_data.get("affection_stage", "stranger")
            affection_behavior = self.get_affection_behavior(affection_level)

            # 添加角色当前状态
            current_state = character_data.get("current_info", {})

            enhanced_prompt = f"""
{base_prompt}

当前关系状态：{affection_level}
行为调整：{affection_behavior}

角色当前状态：
- 对用户的熟悉程度：{current_state.get('familiarity_level', '一般')}
- 信任程度：{current_state.get('trust_level', 5)}/10
- 共同经历：{', '.join(current_state.get('shared_experiences', []))}

请根据以上信息调整你的回复风格和内容深度。
"""

            return enhanced_prompt

    # 全局角色档案管理器
    profile_manager = CharacterProfileManager(game_db)
```

### 4.3 好感度系统实现

```python
# 好感度系统实现
init python:
    class AffectionSystem:
        def __init__(self, db_manager):
            self.db = db_manager
            self.level_config = self.load_level_config()

        def load_level_config(self):
            """加载好感度等级配置"""
            return {
                "stranger": {"min": 0, "max": 19, "name": "陌生人"},
                "friend": {"min": 20, "max": 39, "name": "普通朋友"},
                "good_friend": {"min": 40, "max": 59, "name": "好朋友"},
                "best_friend": {"min": 60, "max": 79, "name": "挚友"},
                "special": {"min": 80, "max": 100, "name": "特殊关系"}
            }

        def get_current_level(self, user_id, character_id):
            """获取当前好感度等级"""
            affection_data = self.db.get_affection_record(user_id, character_id)

            if not affection_data:
                return "stranger", 0

            current_points = affection_data.get("current_level", 0)

            for level_key, config in self.level_config.items():
                if config["min"] <= current_points <= config["max"]:
                    return level_key, current_points

            return "stranger", 0

        def change_affection(self, user_id, character_id, change_amount, reason):
            """改变好感度"""
            current_level, current_points = self.get_current_level(user_id, character_id)

            new_points = max(0, min(100, current_points + change_amount))
            new_level = self.get_level_by_points(new_points)

            # 更新数据库
            self.db.update_affection(user_id, character_id, change_amount, reason)

            # 检查是否升级
            if new_level != current_level:
                self.trigger_level_change_event(user_id, character_id, current_level, new_level)

            return new_level, new_points

    # 全局好感度系统
    affection_system = AffectionSystem(game_db)
```

## 5. API接口设计

### 5.1 RESTful API接口

```python
# Flask API服务器示例
from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import uuid

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://user:pass@localhost/gamedb'
db = SQLAlchemy(app)

# 用户角色API
@app.route('/api/users/<user_id>/characters', methods=['GET'])
def get_user_characters(user_id):
    """获取用户的所有角色"""
    characters = UserCharacter.query.filter_by(user_id=user_id).all()

    result = []
    for char in characters:
        char_data = {
            "id": str(char.id),
            "character_name": char.character_name,
            "current_info": char.current_info,
            "is_unlocked": char.is_unlocked,
            "updated_at": char.updated_at.isoformat()
        }
        result.append(char_data)

    return jsonify({"characters": result})

@app.route('/api/users/<user_id>/characters/<character_id>', methods=['GET'])
def get_user_character(user_id, character_id):
    """获取特定角色信息"""
    character = UserCharacter.query.filter_by(
        user_id=user_id,
        id=character_id
    ).first()

    if not character:
        return jsonify({"error": "Character not found"}), 404

    # 获取角色状态
    state = CharacterState.query.filter_by(user_character_id=character.id).first()

    # 获取好感度
    affection = AffectionRecord.query.filter_by(
        user_id=user_id,
        character_id=character.id
    ).first()

    result = {
        "id": str(character.id),
        "character_name": character.character_name,
        "current_info": character.current_info,
        "appearance_state": character.appearance_state,
        "personality_development": character.personality_development,
        "state": {
            "current_emotion": state.current_emotion if state else "normal",
            "emotion_intensity": state.emotion_intensity if state else 5,
            "activity_status": state.activity_status if state else "available"
        },
        "affection": {
            "current_level": affection.current_level if affection else 0,
            "affection_stage": affection.affection_stage if affection else "stranger"
        }
    }

    return jsonify(result)

# 好感度API
@app.route('/api/users/<user_id>/characters/<character_id>/affection', methods=['POST'])
def update_affection(user_id, character_id):
    """更新好感度"""
    data = request.get_json()

    change_amount = data.get('change_amount', 0)
    reason = data.get('reason', '')

    # 获取或创建好感度记录
    affection = AffectionRecord.query.filter_by(
        user_id=user_id,
        character_id=character_id
    ).first()

    if not affection:
        affection = AffectionRecord(
            user_id=user_id,
            character_id=character_id,
            current_level=0
        )
        db.session.add(affection)

    # 更新好感度
    old_level = affection.current_level
    new_level = max(0, min(100, old_level + change_amount))
    affection.current_level = new_level
    affection.updated_at = datetime.utcnow()

    # 更新好感度阶段
    affection.affection_stage = get_affection_stage(new_level)

    db.session.commit()

    return jsonify({
        "success": True,
        "old_level": old_level,
        "new_level": new_level,
        "affection_stage": affection.affection_stage
    })

def get_affection_stage(points):
    """根据分数获取好感度阶段"""
    if points < 20:
        return "stranger"
    elif points < 40:
        return "friend"
    elif points < 60:
        return "good_friend"
    elif points < 80:
        return "best_friend"
    else:
        return "special"
```

## 6. 性能优化和安全考虑

### 6.1 性能优化策略

**数据库优化：**
- 使用连接池管理数据库连接
- 实施读写分离，读操作使用从库
- 对热点数据使用Redis缓存
- 定期清理过期的对话历史

**缓存策略：**
- 角色基础信息缓存（长期缓存）
- 好感度数据缓存（中期缓存）
- 最近对话缓存（短期缓存）
- 用户会话状态缓存

**API优化：**
- 实施API限流，防止恶意请求
- 使用CDN加速静态资源
- 压缩API响应数据
- 异步处理非关键操作

### 6.2 数据安全措施

**数据加密：**
- 敏感数据字段加密存储
- API传输使用HTTPS
- 数据库连接加密
- 定期更换加密密钥

**访问控制：**
- JWT token认证
- 用户数据隔离
- API权限控制
- 操作日志记录

**备份策略：**
- 每日自动数据库备份
- 增量备份和全量备份结合
- 异地备份存储
- 定期恢复测试

## 7. 部署方案

### 7.1 Docker容器化部署

```yaml
# Docker Compose配置
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: gamedb
      MYSQL_USER: gameuser
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"

  api-server:
    build: ./api
    environment:
      DATABASE_URL: mysql+pymysql://gameuser:${DB_PASSWORD}@mysql:3306/gamedb
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
    depends_on:
      - mysql
      - redis
    ports:
      - "8000:8000"

  nginx:
    image: nginx:alpine
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
    ports:
      - "443:443"
      - "80:80"
    depends_on:
      - api-server

volumes:
  mysql_data:
  redis_data:
```

### 7.2 开发阶段规划

**第一阶段（2-3周）：基础架构**
- 数据库设计和创建
- 基础API开发
- Ren'Py数据库集成

**第二阶段（2-3周）：核心功能**
- 角色档案系统
- 好感度系统
- 记忆管理系统

**第三阶段（1-2周）：优化完善**
- 性能优化
- 安全加固
- 测试和调试

## 8. MySQL使用注意事项

### 8.1 MySQL版本要求
- **推荐版本**：MySQL 8.0+（最佳JSON支持）
- **最低版本**：MySQL 5.7+（基础JSON支持）
- **不支持**：MySQL 5.6及以下（无JSON字段支持）

### 8.2 JSON字段使用注意事项

```python
# Python中JSON字段的正确使用方式
from sqlalchemy import JSON
from sqlalchemy.dialects.mysql import JSON as MySQL_JSON

class UserProfile(db.Model):
    __tablename__ = 'user_profiles'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    preferences = db.Column(MySQL_JSON)  # 使用MySQL专用JSON类型

    def set_preference(self, key, value):
        """安全地设置JSON字段值"""
        if self.preferences is None:
            self.preferences = {}

        # 创建新的字典对象，触发SQLAlchemy的变更检测
        new_prefs = dict(self.preferences)
        new_prefs[key] = value
        self.preferences = new_prefs
```

### 8.3 字符集配置重要性

```sql
-- 确保支持emoji和特殊字符
CREATE DATABASE gamedb
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;
```

### 8.4 与PostgreSQL的主要差异

| 特性 | MySQL | PostgreSQL |
|------|-------|------------|
| UUID支持 | UUID()函数 | gen_random_uuid() |
| JSON字段 | JSON | JSONB |
| 外键语法 | FOREIGN KEY | REFERENCES |
| 自动更新 | ON UPDATE CURRENT_TIMESTAMP | 触发器 |

## 9. 总结

本技术方案提供了一个完整的多用户多AI智能体游戏角色档案系统，具备以下核心优势：

1. **多用户数据隔离**：每个用户拥有独立的游戏世界和角色关系
2. **动态角色发展**：角色会根据互动历史发生真实的变化
3. **智能好感度系统**：基于用户行为自动调整角色态度
4. **长期记忆管理**：彻底解决Token限制问题
5. **个性化AI体验**：每个用户都有独特的角色互动体验
6. **MySQL优化适配**：针对MySQL数据库的专门优化

**预期效果：**
- 用户留存率提升30-50%
- 角色互动真实感显著增强
- 支持长期游戏体验
- 为后续功能扩展提供坚实基础

**开发周期：6-8周**
**技术复杂度：中高等**
**投资回报：高**

**MySQL方案优势：**
- 运维成本低，社区支持好
- 管理工具丰富，学习成本低
- 性能稳定，适合中小型项目
- 与现有技术栈兼容性好

这个系统将为多AI智能体游戏提供强大的技术支撑，实现真正意义上的个性化AI角色互动体验。

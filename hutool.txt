import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.util.*;

public class LiuRenJsonParserHutool {
    
    // 地支顺序数组
    private static final String[] DI_ZHI_ORDER = {
        "子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"
    };
    
    // 天将名称映射
    private static final Map<String, String> TIAN_JIANG_MAP = new HashMap<>() {{
        put("贵", "贵人");
        put("蛇", "螣蛇");
        put("朱", "朱雀");
        put("合", "六合");
        put("勾", "勾陈");
        put("青", "青龙");
        put("空", "天空");
        put("虎", "白虎");
        put("常", "太常");
        put("玄", "玄武");
        put("阴", "太阴");
        put("后", "天后");
    }};

    // 六亲关系映射
    private static final Map<String, String> LIU_QIN_MAP = new HashMap<>() {{
        put("父", "父母");
        put("财", "妻财");
        put("兄", "兄弟");
        put("子", "子孙");
        put("官", "官鬼");
    }};
    
    public static void main(String[] args) {
        String jsonStr = "{\"nowTianPan\":[\"未\",\"申\",\"酉\",\"戌\",\"亥\",\"子\",\"丑\",\"寅\",\"卯\",\"辰\",\"巳\",\"午\"],\"siKe\":[[\"甲\",\"酉\"],[\"酉\",\"辰\"],[\"子\",\"未\"],[\"未\",\"寅\"]],\"sanChuan\":[\"寅\",\"酉\",\"辰\"],\"guiRenArr\":{\"丑\":\"贵\",\"子\":\"蛇\",\"亥\":\"朱\",\"戌\":\"合\",\"酉\":\"勾\",\"申\":\"青\",\"未\":\"空\",\"午\":\"虎\",\"巳\":\"常\",\"辰\":\"玄\",\"卯\":\"阴\",\"寅\":\"后\"},\"xunTianPan\":{\"未\":\"辛\",\"申\":\"壬\",\"酉\":\"癸\",\"戌\":\"◎\",\"kongWang\":[\"戌\",\"亥\"],\"xianKong\":[\"巳\",\"午\"],\"亥\":\"◎\",\"子\":\"甲\",\"丑\":\"乙\",\"寅\":\"丙\",\"卯\":\"丁\",\"辰\":\"戊\",\"巳\":\"己\",\"午\":\"庚\"},\"diZhiLiuQin\":{\"子\":\"父\",\"丑\":\"财\",\"寅\":\"兄\",\"卯\":\"兄\",\"辰\":\"财\",\"巳\":\"子\",\"午\":\"子\",\"未\":\"财\",\"申\":\"官\",\"酉\":\"官\",\"戌\":\"财\",\"亥\":\"父\"},\"YueJiang\":\"申\",\"dayXun\":\"甲子\"}";
        
        String result = parseLiuRenJson(jsonStr);
        System.out.println(result);
    }
    
    public static String parseLiuRenJson(String jsonStr) {
        JSONObject json = JSONUtil.parseObj(jsonStr);
        StringBuilder sb = new StringBuilder();
        
        // 解析月将
        String yueJiang = json.getStr("YueJiang");
        sb.append("月将：").append(yueJiang).append("\n\n");
        
        // 解析天地盘
        sb.append("天地盘：\n\n");
        JSONArray nowTianPan = json.getJSONArray("nowTianPan");
        for (int i = 0; i < DI_ZHI_ORDER.length; i++) {
            String diPan = DI_ZHI_ORDER[i];
            String tianPan = nowTianPan.getStr(i);
            sb.append("- 地盘").append(diPan).append("位上的天盘地支：").append(tianPan).append("\n");
        }
        sb.append("\n");
        
        // 解析四课
        sb.append("四课：\n");
        JSONArray siKe = json.getJSONArray("siKe");
        String[] keNames = {"第一课", "第二课", "第三课", "第四课"};
        for (int i = 0; i < siKe.size(); i++) {
            JSONArray ke = siKe.getJSONArray(i);
            String gan = ke.getStr(0);
            String zhi = ke.getStr(1);
            
            if (i == 0) {
                sb.append("- ").append(keNames[i]).append("：").append(gan).append("（日干）上见").append(zhi).append("\n");
            } else if (i == 1) {
                sb.append("- ").append(keNames[i]).append("：").append(gan).append("（地支）上见").append(zhi).append("（天盘地支）").append("\n");
            } else if (i == 2) {
                sb.append("- ").append(keNames[i]).append("：").append(gan).append("（日支）上见").append(zhi).append("\n");
            } else {
                sb.append("- ").append(keNames[i]).append("：").append(gan).append("（地支）上见").append(zhi).append("（天盘地支）").append("\n");
            }
        }
        sb.append("\n");
        
        // 解析三传
        sb.append("三传：\n");
        JSONArray sanChuan = json.getJSONArray("sanChuan");
        String[] chuanNames = {"初传", "中传", "末传"};
        for (int i = 0; i < sanChuan.size(); i++) {
            sb.append("- ").append(chuanNames[i]).append("：").append(sanChuan.getStr(i)).append("\n");
        }
        sb.append("\n");
        
        // 解析天将排列
        sb.append("天将排列：\n");
        JSONObject guiRenArr = json.getJSONObject("guiRenArr");
        for (String diZhi : DI_ZHI_ORDER) {
            if (guiRenArr.containsKey(diZhi)) {
                String tianJiangCode = guiRenArr.getStr(diZhi);
                String tianJiangName = TIAN_JIANG_MAP.getOrDefault(tianJiangCode, tianJiangCode);
                sb.append("- 天盘地支").append(diZhi).append("配天将：").append(tianJiangName).append("\n");
            }
        }
        sb.append("\n");
        
        // 解析六亲关系
        sb.append("六亲关系：\n");
        JSONObject diZhiLiuQin = json.getJSONObject("diZhiLiuQin");
        for (String diZhi : DI_ZHI_ORDER) {
            if (diZhiLiuQin.containsKey(diZhi)) {
                String liuQinCode = diZhiLiuQin.getStr(diZhi);
                String liuQinName = LIU_QIN_MAP.getOrDefault(liuQinCode, liuQinCode);
                sb.append("- ").append(diZhi).append("为日干的").append(liuQinName).append("\n");
            }
        }
        sb.append("\n");
        
        // 解析空亡和陷空
        JSONObject xunTianPan = json.getJSONObject("xunTianPan");
        if (xunTianPan.containsKey("kongWang")) {
            JSONArray kongWang = xunTianPan.getJSONArray("kongWang");
            sb.append("空亡：");
            for (int i = 0; i < kongWang.size(); i++) {
                if (i > 0) sb.append("、");
                sb.append(kongWang.getStr(i));
            }
            sb.append("\n\n");
        }
        
        if (xunTianPan.containsKey("xianKong")) {
            JSONArray xianKong = xunTianPan.getJSONArray("xianKong");
            sb.append("陷空：");
            for (int i = 0; i < xianKong.size(); i++) {
                if (i > 0) sb.append("、");
                sb.append(xianKong.getStr(i));
            }
            sb.append("\n\n");
        }
        
        // 解析遁干
        sb.append("遁干：");
        List<String> dunGanList = new ArrayList<>();
        for (String diZhi : DI_ZHI_ORDER) {
            if (xunTianPan.containsKey(diZhi) && !"◎".equals(xunTianPan.getStr(diZhi))) {
                String gan = xunTianPan.getStr(diZhi);
                dunGanList.add(gan + diZhi);
            }
        }
        sb.append(String.join("、", dunGanList));
        
        return sb.toString();
    }
}
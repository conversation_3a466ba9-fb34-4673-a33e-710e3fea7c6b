# 多AI智能体游戏记忆库API接口调用说明文档（字节跳动记忆库专用）

## 1. 记忆库服务接口概览

### 1.1 基础信息

**API基础地址**：`https://memory-api.bytedance.com/v1`

**认证方式**：Bearer Token认证

**通用请求头**：
```http
Authorization: Bearer {access_token}
Content-Type: application/json
Accept: application/json
X-User-ID: {user_id}
X-Character-ID: {character_id}
```

### 1.2 接口分类

| 分类 | 功能描述 | 接口数量 |
|------|----------|----------|
| 记忆创建 | 创建各类型记忆数据 | 2个 |
| 记忆检索 | 语义搜索和条件查询 | 3个 |
| 记忆更新 | 更新和删除记忆 | 2个 |
| 批量操作 | 批量处理记忆数据 | 1个 |

## 2. 记忆创建接口

### 2.1 创建单个记忆

**接口地址**：`POST /memory/create`

**请求头部**：
```http
Authorization: Bearer {access_token}
Content-Type: application/json
X-User-ID: {user_id}
X-Character-ID: {character_id}
```

**请求参数**：
```json
{
  "memory_type": "conversation",
  "content": {
    "summary": "用户询问了关于考试的问题，角色给出了鼓励性回复",
    "key_points": ["考试焦虑", "情感支持", "学习建议"],
    "emotional_state": {
      "user_emotion": "anxious",
      "character_emotion": "supportive",
      "overall_mood": "caring"
    },
    "context_tags": ["学习", "考试", "情感支持"],
    "participants": ["user_12345", "xiaoxue"],
    "conversation_turn": 15
  },
  "importance_score": 0.8,
  "expires_at": "2024-03-15T10:00:00Z",
  "metadata": {
    "source": "game_conversation",
    "session_id": "sess_20240115_001",
    "scene": "classroom"
  }
}
```

**参数说明**：
- `memory_type`：记忆类型，枚举值：
  - `core_personality`：角色核心人格
  - `relationship`：用户关系状态
  - `conversation`：对话记忆
  - `event`：重要事件
  - `preference`：用户偏好
- `content`：记忆内容对象，结构根据memory_type变化
- `importance_score`：重要性评分，范围0.0-1.0
- `expires_at`：过期时间，可选，ISO 8601格式
- `metadata`：元数据信息，用于分类和检索

**成功响应**：
```json
{
  "code": 200,
  "message": "Memory created successfully",
  "data": {
    "memory_id": "mem_20240115_001",
    "user_id": "user_12345",
    "character_id": "xiaoxue",
    "memory_type": "conversation",
    "created_at": "2024-01-15T10:30:00Z",
    "status": "active",
    "storage_size": 1024
  }
}
```

### 2.2 创建关系记忆

**接口地址**：`POST /memory/relationship/create`

**请求参数**：
```json
{
  "relationship_data": {
    "level": "好朋友",
    "affection_score": 75,
    "trust_level": 8,
    "interaction_count": 156,
    "relationship_history": [
      {
        "event": "初次见面",
        "date": "2024-01-01",
        "affection_change": 10,
        "description": "在图书馆初次相遇"
      },
      {
        "event": "一起学习",
        "date": "2024-01-10",
        "affection_change": 15,
        "description": "一起在图书馆复习功课"
      }
    ],
    "shared_experiences": [
      {
        "experience": "一起参加社团活动",
        "emotional_impact": "positive",
        "memory_strength": 0.9
      }
    ],
    "personality_compatibility": {
      "compatibility_score": 0.85,
      "common_interests": ["读书", "学习", "音乐"],
      "personality_match": ["都比较内向", "都很善良"]
    }
  },
  "importance_score": 0.95,
  "metadata": {
    "relationship_type": "friendship",
    "development_stage": "stable"
  }
}
```

**成功响应**：
```json
{
  "code": 200,
  "message": "Relationship memory created successfully",
  "data": {
    "memory_id": "rel_20240115_001",
    "relationship_level": "好朋友",
    "affection_score": 75,
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

## 3. 记忆检索接口

### 3.1 语义搜索记忆

**接口地址**：`POST /memory/search`

**请求参数**：
```json
{
  "query": "考试相关的对话",
  "search_type": "semantic",
  "memory_types": ["conversation", "event"],
  "max_results": 10,
  "importance_threshold": 0.6,
  "time_range": {
    "start": "2024-01-01T00:00:00Z",
    "end": "2024-01-15T23:59:59Z"
  },
  "sort_by": "relevance",
  "include_expired": false,
  "filters": {
    "context_tags": ["学习", "考试"],
    "emotional_state": ["anxious", "supportive"],
    "scene": ["classroom", "library"]
  }
}
```

**参数说明**：
- `query`：搜索查询字符串，支持自然语言语义搜索
- `search_type`：搜索类型，枚举值：`semantic`、`keyword`、`hybrid`
- `memory_types`：要搜索的记忆类型数组
- `max_results`：最大返回结果数，默认10，最大100
- `importance_threshold`：重要性阈值过滤
- `time_range`：时间范围过滤
- `sort_by`：排序方式，枚举值：`relevance`、`importance`、`time`、`affection_impact`
- `filters`：高级过滤条件

**成功响应**：
```json
{
  "code": 200,
  "message": "Search completed successfully",
  "data": {
    "memories": [
      {
        "memory_id": "mem_20240115_001",
        "memory_type": "conversation",
        "content": {
          "summary": "用户询问了关于考试的问题",
          "key_points": ["考试焦虑", "情感支持"],
          "emotional_state": {
            "user_emotion": "anxious",
            "character_emotion": "supportive"
          },
          "context_tags": ["学习", "考试", "情感支持"]
        },
        "importance_score": 0.8,
        "relevance_score": 0.92,
        "created_at": "2024-01-15T10:30:00Z",
        "last_accessed": "2024-01-15T15:20:00Z",
        "access_count": 3
      }
    ],
    "total_count": 1,
    "search_time_ms": 45,
    "search_metadata": {
      "query_embedding_time": 12,
      "index_search_time": 28,
      "result_ranking_time": 5
    }
  }
}
```

### 3.2 获取核心记忆

**接口地址**：`GET /memory/core`

**查询参数**：
```
?memory_types=core_personality,relationship&include_metadata=true
```

**成功响应**：
```json
{
  "code": 200,
  "message": "Core memories retrieved successfully",
  "data": {
    "core_memories": {
      "personality": {
        "memory_id": "core_xiaoxue_001",
        "content": {
          "personality_traits": ["温柔", "内向", "善良", "聪明"],
          "background_story": "高中二年级学生，成绩优秀，喜欢读书和音乐",
          "speech_style": "温和有礼貌，经常使用敬语",
          "behavioral_patterns": ["学习认真", "关心朋友", "不善表达情感"],
          "core_values": ["友谊", "诚实", "努力"]
        },
        "importance_score": 1.0,
        "last_updated": "2024-01-15T10:00:00Z"
      },
      "relationship": {
        "memory_id": "rel_xiaoxue_user12345",
        "content": {
          "current_level": "好朋友",
          "affection_score": 75,
          "trust_level": 8,
          "relationship_summary": "通过学习建立的深厚友谊"
        },
        "importance_score": 0.95,
        "last_updated": "2024-01-15T15:45:00Z"
      }
    }
  }
}
```

### 3.3 获取最近记忆

**接口地址**：`GET /memory/recent`

**查询参数**：
```
?days=7&limit=20&memory_types=conversation,event&min_importance=0.5
```

**参数说明**：
- `days`：获取最近N天的记忆，默认7天
- `limit`：返回数量限制，默认20，最大50
- `memory_types`：记忆类型过滤
- `min_importance`：最小重要性阈值

**成功响应**：
```json
{
  "code": 200,
  "message": "Recent memories retrieved successfully",
  "data": {
    "memories": [
      {
        "memory_id": "mem_20240115_003",
        "memory_type": "conversation",
        "content": {
          "summary": "讨论了学习方法和时间管理",
          "emotional_impact": "positive"
        },
        "importance_score": 0.7,
        "created_at": "2024-01-15T14:20:00Z"
      }
    ],
    "total_count": 15,
    "time_range": {
      "start": "2024-01-08T00:00:00Z",
      "end": "2024-01-15T23:59:59Z"
    }
  }
}
```

## 4. 记忆更新接口

### 4.1 更新记忆内容

**接口地址**：`PUT /memory/{memory_id}`

**URL参数**：
- `memory_id`：记忆ID

**请求参数**：
```json
{
  "updates": {
    "content": {
      "summary": "更新后的对话摘要",
      "key_points": ["新的关键点1", "新的关键点2"]
    },
    "importance_score": 0.9,
    "metadata": {
      "last_referenced": "2024-01-15T16:00:00Z",
      "reference_count": 5
    }
  },
  "update_type": "partial"
}
```

**参数说明**：
- `updates`：要更新的字段和值
- `update_type`：更新类型，枚举值：`partial`（部分更新）、`full`（完全替换）

**成功响应**：
```json
{
  "code": 200,
  "message": "Memory updated successfully",
  "data": {
    "memory_id": "mem_20240115_001",
    "updated_fields": ["content.summary", "importance_score", "metadata"],
    "updated_at": "2024-01-15T16:00:00Z",
    "version": 2
  }
}
```

### 4.2 删除记忆

**接口地址**：`DELETE /memory/{memory_id}`

**查询参数**：
```
?soft_delete=true&reason=expired
```

**参数说明**：
- `soft_delete`：是否软删除，默认true
- `reason`：删除原因，可选值：`expired`、`low_importance`、`user_request`

**成功响应**：
```json
{
  "code": 200,
  "message": "Memory deleted successfully",
  "data": {
    "memory_id": "mem_20240115_001",
    "deleted_at": "2024-01-15T16:30:00Z",
    "delete_type": "soft",
    "recoverable_until": "2024-01-22T16:30:00Z"
  }
}

## 5. 批量操作接口

### 5.1 批量记忆操作

**接口地址**：`POST /memory/batch`

**请求参数**：
```json
{
  "operations": [
    {
      "operation": "create",
      "memory": {
        "memory_type": "conversation",
        "content": {
          "summary": "新的对话记忆",
          "key_points": ["关键点1", "关键点2"]
        },
        "importance_score": 0.7
      }
    },
    {
      "operation": "update",
      "memory_id": "mem_20240115_001",
      "updates": {
        "last_accessed": "2024-01-15T16:00:00Z",
        "access_count": 5
      }
    },
    {
      "operation": "delete",
      "memory_id": "mem_20240114_003",
      "soft_delete": true
    }
  ],
  "transaction": true,
  "max_failures": 0
}
```

**参数说明**：
- `operations`：操作数组，支持create、update、delete操作
- `transaction`：是否作为事务执行，默认false
- `max_failures`：允许的最大失败数，超过则回滚，默认0

**成功响应**：
```json
{
  "code": 200,
  "message": "Batch operations completed successfully",
  "data": {
    "transaction_id": "txn_20240115_001",
    "results": [
      {
        "operation": "create",
        "status": "success",
        "memory_id": "mem_20240115_004",
        "execution_time_ms": 45
      },
      {
        "operation": "update",
        "status": "success",
        "memory_id": "mem_20240115_001",
        "execution_time_ms": 23
      },
      {
        "operation": "delete",
        "status": "success",
        "memory_id": "mem_20240114_003",
        "execution_time_ms": 12
      }
    ],
    "summary": {
      "total_operations": 3,
      "successful_operations": 3,
      "failed_operations": 0,
      "total_execution_time_ms": 80
    }
  }
}
```

## 6. 高级功能接口

### 6.1 记忆关联分析

**接口地址**：`POST /memory/analyze/associations`

**请求参数**：
```json
{
  "target_memory_id": "mem_20240115_001",
  "analysis_type": "semantic_similarity",
  "max_associations": 10,
  "similarity_threshold": 0.7,
  "include_types": ["conversation", "event"],
  "time_window_days": 30
}
```

**成功响应**：
```json
{
  "code": 200,
  "message": "Memory associations analyzed successfully",
  "data": {
    "target_memory": {
      "memory_id": "mem_20240115_001",
      "summary": "关于考试的对话"
    },
    "associations": [
      {
        "memory_id": "mem_20240110_002",
        "similarity_score": 0.89,
        "association_type": "semantic_similarity",
        "common_elements": ["考试", "学习", "焦虑"],
        "relationship_strength": "strong"
      }
    ],
    "analysis_metadata": {
      "total_candidates": 156,
      "analysis_time_ms": 234
    }
  }
}
```

### 6.2 记忆重要性重新评估

**接口地址**：`POST /memory/reevaluate/importance`

**请求参数**：
```json
{
  "evaluation_scope": {
    "memory_types": ["conversation", "event"],
    "time_range": {
      "start": "2024-01-01T00:00:00Z",
      "end": "2024-01-15T23:59:59Z"
    },
    "current_importance_range": {
      "min": 0.5,
      "max": 1.0
    }
  },
  "evaluation_criteria": {
    "recency_weight": 0.3,
    "frequency_weight": 0.2,
    "emotional_impact_weight": 0.3,
    "relationship_impact_weight": 0.2
  },
  "dry_run": false
}
```

**成功响应**：
```json
{
  "code": 200,
  "message": "Importance reevaluation completed successfully",
  "data": {
    "evaluation_id": "eval_20240115_001",
    "processed_memories": 45,
    "updated_memories": 12,
    "changes_summary": {
      "importance_increased": 8,
      "importance_decreased": 4,
      "no_change": 33
    },
    "significant_changes": [
      {
        "memory_id": "mem_20240110_001",
        "old_importance": 0.6,
        "new_importance": 0.85,
        "change_reason": "high_emotional_impact"
      }
    ]
  }
}

## 7. 错误处理

### 7.1 错误响应格式

```json
{
  "code": 400,
  "message": "Invalid request parameters",
  "error_code": "INVALID_MEMORY_TYPE",
  "details": {
    "field": "memory_type",
    "value": "invalid_type",
    "allowed_values": ["core_personality", "relationship", "conversation", "event", "preference"]
  },
  "request_id": "req_20240115_001",
  "timestamp": "2024-01-15T15:30:00Z",
  "trace_id": "trace_abc123def456"
}
```

### 7.2 常见错误码

| 错误码 | HTTP状态码 | 说明 | 解决方案 |
|--------|------------|------|----------|
| INVALID_MEMORY_TYPE | 400 | 无效的记忆类型 | 检查memory_type参数值 |
| MEMORY_NOT_FOUND | 404 | 记忆不存在 | 确认memory_id是否正确 |
| QUOTA_EXCEEDED | 429 | 配额超限 | 等待配额重置或升级套餐 |
| CONTENT_TOO_LARGE | 413 | 内容过大 | 减少content字段大小 |
| INVALID_TIME_RANGE | 400 | 时间范围无效 | 检查时间格式和范围 |
| SEARCH_TIMEOUT | 408 | 搜索超时 | 缩小搜索范围或简化查询 |
| BATCH_SIZE_EXCEEDED | 400 | 批量操作数量超限 | 减少operations数组大小 |
| TRANSACTION_FAILED | 500 | 事务执行失败 | 检查操作参数并重试 |

## 8. 性能和限制

### 8.1 API限制

| 项目 | 限制 |
|------|------|
| 单次记忆内容大小 | 50KB |
| 批量操作数量 | 100个/次 |
| 搜索结果数量 | 100条/次 |
| 查询字符串长度 | 1000字符 |
| 并发请求数 | 50个/秒 |

### 8.2 配额管理

**查询配额使用情况**：
```http
GET /quota/usage?date=2024-01-15
```

**响应示例**：
```json
{
  "code": 200,
  "data": {
    "user_id": "user_12345",
    "date": "2024-01-15",
    "quota_usage": {
      "memory_operations": {
        "used": 245,
        "limit": 10000,
        "remaining": 9755,
        "reset_time": "2024-01-16T00:00:00Z"
      },
      "search_operations": {
        "used": 89,
        "limit": 5000,
        "remaining": 4911,
        "reset_time": "2024-01-16T00:00:00Z"
      },
      "storage_usage": {
        "used_mb": 15.6,
        "limit_mb": 1000,
        "remaining_mb": 984.4
      }
    }
  }
}
```

## 9. 使用示例

### 9.1 创建对话记忆示例

```bash
curl -X POST "https://memory-api.bytedance.com/v1/memory/create" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user_12345" \
  -H "X-Character-ID: xiaoxue" \
  -d '{
    "memory_type": "conversation",
    "content": {
      "summary": "用户询问考试相关问题，角色给予鼓励",
      "key_points": ["考试焦虑", "情感支持", "学习建议"],
      "emotional_state": {
        "user_emotion": "anxious",
        "character_emotion": "supportive",
        "overall_mood": "caring"
      },
      "context_tags": ["学习", "考试", "情感支持"]
    },
    "importance_score": 0.8
  }'
```

### 9.2 语义搜索示例

```bash
curl -X POST "https://memory-api.bytedance.com/v1/memory/search" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user_12345" \
  -H "X-Character-ID: xiaoxue" \
  -d '{
    "query": "关于学习和考试的对话",
    "search_type": "semantic",
    "memory_types": ["conversation"],
    "max_results": 5,
    "importance_threshold": 0.6,
    "sort_by": "relevance"
  }'
```

### 9.3 批量操作示例

```bash
curl -X POST "https://memory-api.bytedance.com/v1/memory/batch" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user_12345" \
  -H "X-Character-ID: xiaoxue" \
  -d '{
    "operations": [
      {
        "operation": "create",
        "memory": {
          "memory_type": "event",
          "content": {
            "event_name": "期末考试",
            "description": "用户参加期末考试",
            "emotional_impact": "stressful"
          },
          "importance_score": 0.9
        }
      },
      {
        "operation": "update",
        "memory_id": "mem_20240115_001",
        "updates": {
          "last_accessed": "2024-01-15T16:00:00Z"
        }
      }
    ],
    "transaction": true
  }'
```

## 10. 最佳实践

### 10.1 记忆内容设计建议

1. **结构化内容**：使用清晰的JSON结构组织记忆内容
2. **关键词标签**：合理使用context_tags便于检索
3. **重要性评分**：准确评估记忆重要性，便于后续管理
4. **过期时间**：为临时记忆设置合理的过期时间
5. **元数据利用**：充分利用metadata字段存储辅助信息

### 10.2 检索优化建议

1. **语义搜索**：优先使用语义搜索获得更好的相关性
2. **过滤条件**：合理使用过滤条件缩小搜索范围
3. **批量检索**：需要多个记忆时使用批量接口
4. **缓存策略**：对频繁访问的记忆进行客户端缓存
5. **分页处理**：大量结果时使用分页避免超时

### 10.3 性能优化建议

1. **批量操作**：尽量使用批量接口减少API调用
2. **异步处理**：非关键路径操作使用异步处理
3. **连接复用**：使用HTTP连接池提高效率
4. **压缩传输**：启用gzip压缩减少传输时间
5. **监控告警**：设置关键指标监控及时发现问题

通过这份专门针对字节跳动记忆库的API接口文档，开发者可以高效地集成和使用记忆库服务，实现智能的角色记忆管理功能。
```
```

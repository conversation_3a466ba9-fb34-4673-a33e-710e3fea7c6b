# 多AI智能体游戏记忆库API接口调用说明文档

## 1. 接口概览

### 1.1 基础信息

**API基础地址**：
- 记忆库服务：`https://memory-api.bytedance.com/v1`
- 火山引擎群聊：`https://ark.cn-beijing.volces.com/api/v3`
- 游戏服务网关：`https://game-api.yourcompany.com/v1`

**认证方式**：
- 记忆库：Bearer Token认证
- 火山引擎：API Key认证
- 游戏服务：JWT Token认证

**通用请求头**：
```http
Content-Type: application/json
Accept: application/json
User-Agent: GameService/1.0
```

### 1.2 接口分类

| 分类 | 功能描述 | 接口数量 |
|------|----------|----------|
| 记忆管理 | 记忆的创建、检索、更新、删除 | 8个 |
| 对话处理 | 群聊API调用和上下文管理 | 4个 |
| 会话管理 | 用户会话的创建和维护 | 6个 |
| 状态同步 | 角色状态和关系数据同步 | 5个 |

## 2. 记忆管理接口

### 2.1 创建记忆

**接口地址**：`POST /memory/create`

**请求头部**：
```http
Authorization: Bearer {access_token}
Content-Type: application/json
X-User-ID: {user_id}
X-Character-ID: {character_id}
```

**请求参数**：
```json
{
  "memory_type": "conversation",
  "content": {
    "summary": "用户询问了关于考试的问题，角色给出了鼓励性回复",
    "key_points": ["考试焦虑", "情感支持", "学习建议"],
    "emotional_state": {
      "user_emotion": "anxious",
      "character_emotion": "supportive",
      "overall_mood": "caring"
    },
    "context_tags": ["学习", "考试", "情感支持"]
  },
  "importance_score": 0.8,
  "expires_at": "2024-03-15T10:00:00Z"
}
```

**参数说明**：
- `memory_type`：记忆类型，枚举值：`core_personality`、`relationship`、`conversation`、`event`
- `content`：记忆内容对象，结构根据memory_type变化
- `importance_score`：重要性评分，范围0.0-1.0
- `expires_at`：过期时间，可选，ISO 8601格式

**成功响应**：
```json
{
  "code": 200,
  "message": "Memory created successfully",
  "data": {
    "memory_id": "mem_20240115_001",
    "created_at": "2024-01-15T10:30:00Z",
    "status": "active"
  }
}
```

**错误响应**：
```json
{
  "code": 400,
  "message": "Invalid memory type",
  "error_code": "INVALID_MEMORY_TYPE",
  "details": {
    "field": "memory_type",
    "allowed_values": ["core_personality", "relationship", "conversation", "event"]
  }
}
```

### 2.2 检索记忆

**接口地址**：`POST /memory/search`

**请求头部**：
```http
Authorization: Bearer {access_token}
Content-Type: application/json
X-User-ID: {user_id}
X-Character-ID: {character_id}
```

**请求参数**：
```json
{
  "query": "考试相关的对话",
  "memory_types": ["conversation", "relationship"],
  "max_results": 10,
  "importance_threshold": 0.6,
  "time_range": {
    "start": "2024-01-01T00:00:00Z",
    "end": "2024-01-15T23:59:59Z"
  },
  "sort_by": "relevance",
  "include_expired": false
}
```

**参数说明**：
- `query`：搜索查询字符串，支持语义搜索
- `memory_types`：要搜索的记忆类型数组，可选
- `max_results`：最大返回结果数，默认10，最大100
- `importance_threshold`：重要性阈值，只返回高于此值的记忆
- `time_range`：时间范围过滤，可选
- `sort_by`：排序方式，枚举值：`relevance`、`importance`、`time`
- `include_expired`：是否包含已过期记忆，默认false

**成功响应**：
```json
{
  "code": 200,
  "message": "Search completed successfully",
  "data": {
    "memories": [
      {
        "memory_id": "mem_20240115_001",
        "memory_type": "conversation",
        "content": {
          "summary": "用户询问了关于考试的问题",
          "key_points": ["考试焦虑", "情感支持"],
          "emotional_state": {
            "user_emotion": "anxious",
            "character_emotion": "supportive"
          }
        },
        "importance_score": 0.8,
        "relevance_score": 0.92,
        "created_at": "2024-01-15T10:30:00Z",
        "last_accessed": "2024-01-15T15:20:00Z"
      }
    ],
    "total_count": 1,
    "search_time_ms": 45
  }
}
```

### 2.3 批量操作记忆

**接口地址**：`POST /memory/batch`

**请求头部**：
```http
Authorization: Bearer {access_token}
Content-Type: application/json
X-User-ID: {user_id}
X-Character-ID: {character_id}
```

**请求参数**：
```json
{
  "operations": [
    {
      "operation": "create",
      "memory": {
        "memory_type": "conversation",
        "content": {...},
        "importance_score": 0.7
      }
    },
    {
      "operation": "update",
      "memory_id": "mem_20240115_001",
      "updates": {
        "last_accessed": "2024-01-15T16:00:00Z",
        "access_count": 5
      }
    },
    {
      "operation": "delete",
      "memory_id": "mem_20240114_003"
    }
  ]
}
```

**参数说明**：
- `operations`：操作数组，每个操作包含operation字段和相关参数
- `operation`：操作类型，枚举值：`create`、`update`、`delete`
- 其他参数根据操作类型变化

**成功响应**：
```json
{
  "code": 200,
  "message": "Batch operations completed",
  "data": {
    "results": [
      {
        "operation": "create",
        "status": "success",
        "memory_id": "mem_20240115_002"
      },
      {
        "operation": "update",
        "status": "success",
        "memory_id": "mem_20240115_001"
      },
      {
        "operation": "delete",
        "status": "success",
        "memory_id": "mem_20240114_003"
      }
    ],
    "success_count": 3,
    "failure_count": 0
  }
}
```

## 3. 对话处理接口

### 3.1 生成AI回复

**接口地址**：`POST /chat/completions`

**请求头部**：
```http
Authorization: Bearer {volcano_api_key}
Content-Type: application/json
X-Request-ID: {unique_request_id}
```

**请求参数**：
```json
{
  "model": "ep-20241201000000-xxxxx",
  "messages": [
    {
      "role": "system",
      "content": "你是小雪，一个温柔善良的高中生。你和用户是好朋友，你们经常一起学习。当前好感度：75分。最近你们讨论过考试的话题，你对用户的学习很关心。"
    },
    {
      "role": "user",
      "content": "小雪，我今天考试考得不太好，有点沮丧。"
    }
  ],
  "max_tokens": 200,
  "temperature": 0.7,
  "top_p": 0.9,
  "stream": false
}
```

**参数说明**：
- `model`：模型ID，由火山引擎提供
- `messages`：对话消息数组，包含system、user、assistant角色
- `max_tokens`：最大生成token数
- `temperature`：随机性控制，0.0-2.0
- `top_p`：核采样参数，0.0-1.0
- `stream`：是否流式返回，默认false

**成功响应**：
```json
{
  "id": "chatcmpl-20240115-001",
  "object": "chat.completion",
  "created": 1705315800,
  "model": "ep-20241201000000-xxxxx",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "哎呀，别太难过啦！考试只是一次检验，不代表你的全部能力。我知道你平时很努力学习的，一次考试不理想很正常。要不我们一起分析一下哪些地方还需要加强？我可以帮你复习哦！"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 156,
    "completion_tokens": 67,
    "total_tokens": 223
  }
}

### 3.2 构建智能上下文

**接口地址**：`POST /context/build`

**请求头部**：
```http
Authorization: Bearer {jwt_token}
Content-Type: application/json
X-User-ID: {user_id}
X-Character-ID: {character_id}
```

**请求参数**：
```json
{
  "current_message": "小雪，我今天考试考得不太好，有点沮丧。",
  "session_history": [
    {
      "role": "user",
      "content": "小雪，明天就要考试了，我有点紧张。",
      "timestamp": "2024-01-14T20:00:00Z"
    },
    {
      "role": "assistant",
      "content": "别紧张，你平时学得很认真，一定没问题的！",
      "timestamp": "2024-01-14T20:01:00Z"
    }
  ],
  "max_context_tokens": 3000,
  "memory_recall_limit": 15,
  "include_relationship_context": true
}
```

**参数说明**：
- `current_message`：用户当前发送的消息
- `session_history`：当前会话的历史消息
- `max_context_tokens`：上下文最大token限制
- `memory_recall_limit`：记忆召回数量限制
- `include_relationship_context`：是否包含关系上下文

**成功响应**：
```json
{
  "code": 200,
  "message": "Context built successfully",
  "data": {
    "context": {
      "character_setting": "你是小雪，一个温柔善良的高中生...",
      "relationship_status": {
        "level": "好朋友",
        "affection_score": 75,
        "trust_level": 8
      },
      "relevant_memories": [
        {
          "memory_id": "mem_20240114_001",
          "content": "用户之前表达过考试焦虑",
          "relevance_score": 0.89
        }
      ],
      "session_summary": "用户对即将到来的考试感到紧张，角色给予了鼓励"
    },
    "token_usage": {
      "character_setting_tokens": 120,
      "relationship_tokens": 45,
      "memory_tokens": 180,
      "session_tokens": 95,
      "total_tokens": 440
    },
    "compression_applied": false
  }
}
```

## 4. 会话管理接口

### 4.1 创建会话

**接口地址**：`POST /session/create`

**请求头部**：
```http
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**请求参数**：
```json
{
  "user_id": "user_12345",
  "character_id": "xiaoxue",
  "session_type": "conversation",
  "initial_context": {
    "scene": "classroom",
    "time_of_day": "afternoon",
    "mood": "casual"
  }
}
```

**参数说明**：
- `user_id`：用户唯一标识
- `character_id`：角色唯一标识
- `session_type`：会话类型，枚举值：`conversation`、`story_mode`、`task_mode`
- `initial_context`：初始上下文信息，可选

**成功响应**：
```json
{
  "code": 200,
  "message": "Session created successfully",
  "data": {
    "session_id": "sess_20240115_001",
    "user_id": "user_12345",
    "character_id": "xiaoxue",
    "status": "active",
    "created_at": "2024-01-15T10:00:00Z",
    "expires_at": "2024-01-15T22:00:00Z",
    "context": {
      "scene": "classroom",
      "time_of_day": "afternoon",
      "mood": "casual"
    }
  }
}
```

### 4.2 获取会话状态

**接口地址**：`GET /session/{session_id}`

**请求头部**：
```http
Authorization: Bearer {jwt_token}
```

**URL参数**：
- `session_id`：会话ID

**查询参数**：
```
?include_history=true&history_limit=20
```

**参数说明**：
- `include_history`：是否包含对话历史，默认false
- `history_limit`：历史消息数量限制，默认10，最大50

**成功响应**：
```json
{
  "code": 200,
  "message": "Session retrieved successfully",
  "data": {
    "session_id": "sess_20240115_001",
    "user_id": "user_12345",
    "character_id": "xiaoxue",
    "status": "active",
    "created_at": "2024-01-15T10:00:00Z",
    "last_activity": "2024-01-15T15:30:00Z",
    "message_count": 12,
    "token_usage": {
      "total_tokens": 1850,
      "remaining_tokens": 2150
    },
    "history": [
      {
        "role": "user",
        "content": "小雪，你好！",
        "timestamp": "2024-01-15T10:05:00Z"
      },
      {
        "role": "assistant",
        "content": "你好！很高兴见到你！",
        "timestamp": "2024-01-15T10:05:15Z"
      }
    ]
  }
}
```

## 5. 状态同步接口

### 5.1 更新角色状态

**接口地址**：`PUT /character/{character_id}/status`

**请求头部**：
```http
Authorization: Bearer {jwt_token}
Content-Type: application/json
X-User-ID: {user_id}
```

**URL参数**：
- `character_id`：角色ID

**请求参数**：
```json
{
  "emotional_state": {
    "primary_emotion": "happy",
    "intensity": 0.7,
    "secondary_emotions": ["excited", "caring"]
  },
  "relationship_updates": {
    "affection_score_delta": 2,
    "trust_level_delta": 1,
    "interaction_count": 1
  },
  "activity_context": {
    "current_activity": "studying",
    "location": "library",
    "companions": ["user_12345"]
  }
}
```

**参数说明**：
- `emotional_state`：情感状态更新
- `relationship_updates`：关系状态变化，使用delta值
- `activity_context`：当前活动上下文

**成功响应**：
```json
{
  "code": 200,
  "message": "Character status updated successfully",
  "data": {
    "character_id": "xiaoxue",
    "updated_at": "2024-01-15T15:45:00Z",
    "current_status": {
      "emotional_state": {
        "primary_emotion": "happy",
        "intensity": 0.7
      },
      "relationship_status": {
        "affection_score": 77,
        "trust_level": 9
      },
      "activity_context": {
        "current_activity": "studying",
        "location": "library"
      }
    }
  }
}

### 5.2 同步用户关系

**接口地址**：`POST /relationship/sync`

**请求头部**：
```http
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**请求参数**：
```json
{
  "user_id": "user_12345",
  "character_id": "xiaoxue",
  "relationship_data": {
    "level": "好朋友",
    "affection_score": 77,
    "trust_level": 9,
    "interaction_count": 156,
    "last_interaction": "2024-01-15T15:45:00Z",
    "shared_experiences": [
      {
        "event": "一起在图书馆学习",
        "date": "2024-01-15",
        "emotional_impact": "positive"
      }
    ],
    "relationship_milestones": [
      {
        "milestone": "成为朋友",
        "achieved_at": "2024-01-10T14:20:00Z",
        "affection_threshold": 50
      }
    ]
  }
}
```

**成功响应**：
```json
{
  "code": 200,
  "message": "Relationship synchronized successfully",
  "data": {
    "sync_id": "sync_20240115_001",
    "user_id": "user_12345",
    "character_id": "xiaoxue",
    "synchronized_at": "2024-01-15T15:50:00Z",
    "changes_applied": [
      "affection_score updated: 75 -> 77",
      "trust_level updated: 8 -> 9",
      "new shared experience added"
    ]
  }
}
```

## 6. 调用流程说明

### 6.1 完整对话处理流程

**步骤1：创建或获取会话**
```http
POST /session/create
或
GET /session/{session_id}
```

**步骤2：构建智能上下文**
```http
POST /context/build
```
- 依赖：需要有效的会话ID
- 前置条件：用户已登录，角色已解锁

**步骤3：检索相关记忆**
```http
POST /memory/search
```
- 依赖：步骤2返回的上下文信息
- 并发执行：可与步骤2并行执行

**步骤4：生成AI回复**
```http
POST /chat/completions
```
- 依赖：步骤2和步骤3的结果
- 前置条件：上下文token数不超过限制

**步骤5：更新角色状态**
```http
PUT /character/{character_id}/status
```
- 依赖：步骤4的AI回复内容
- 异步执行：可在返回用户响应后执行

**步骤6：保存新记忆**
```http
POST /memory/create
或
POST /memory/batch
```
- 依赖：步骤4和步骤5的结果
- 异步执行：可在后台执行

**步骤7：同步关系状态**
```http
POST /relationship/sync
```
- 依赖：步骤5的状态更新
- 异步执行：可批量处理

### 6.2 错误处理流程

**Token超限处理**：
1. 检测到token使用量超过阈值（80%）
2. 调用`POST /context/build`进行智能压缩
3. 如果压缩后仍超限，调用`POST /memory/batch`清理过期记忆
4. 重新构建上下文并继续处理

**记忆服务不可用**：
1. 检测到记忆服务异常
2. 启用本地缓存模式
3. 使用基础角色设定继续对话
4. 记录待同步的操作队列
5. 服务恢复后批量同步数据

**会话过期处理**：
1. 检测到会话已过期
2. 调用`POST /session/create`创建新会话
3. 从记忆库恢复必要的上下文
4. 继续处理用户请求

## 7. 错误码说明

### 7.1 通用错误码

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| 200 | 200 | 请求成功 |
| 400 | 400 | 请求参数错误 |
| 401 | 401 | 认证失败 |
| 403 | 403 | 权限不足 |
| 404 | 404 | 资源不存在 |
| 429 | 429 | 请求频率超限 |
| 500 | 500 | 服务器内部错误 |
| 503 | 503 | 服务不可用 |

### 7.2 业务错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| INVALID_MEMORY_TYPE | 无效的记忆类型 | 检查memory_type参数值 |
| TOKEN_LIMIT_EXCEEDED | Token使用量超限 | 调用上下文压缩接口 |
| SESSION_EXPIRED | 会话已过期 | 创建新会话 |
| CHARACTER_NOT_UNLOCKED | 角色未解锁 | 检查用户游戏进度 |
| MEMORY_SERVICE_UNAVAILABLE | 记忆服务不可用 | 启用降级模式 |
| RELATIONSHIP_DATA_CONFLICT | 关系数据冲突 | 调用关系同步接口 |

### 7.3 错误响应格式

```json
{
  "code": 400,
  "message": "Invalid request parameters",
  "error_code": "INVALID_MEMORY_TYPE",
  "details": {
    "field": "memory_type",
    "value": "invalid_type",
    "allowed_values": ["core_personality", "relationship", "conversation", "event"]
  },
  "request_id": "req_20240115_001",
  "timestamp": "2024-01-15T15:30:00Z"
}
```

## 8. 调用限制和配额

### 8.1 频率限制

| 接口类型 | 限制 | 时间窗口 |
|----------|------|----------|
| 记忆检索 | 100次/分钟 | 每用户 |
| 记忆创建 | 50次/分钟 | 每用户 |
| AI对话 | 30次/分钟 | 每用户 |
| 状态更新 | 200次/分钟 | 每用户 |

### 8.2 数据限制

| 项目 | 限制 |
|------|------|
| 单次记忆内容大小 | 10KB |
| 批量操作数量 | 100个/次 |
| 会话历史长度 | 1000条消息 |
| 上下文最大token | 4096个 |
| 记忆检索结果 | 100条/次 |

### 8.3 配额管理

**查询配额使用情况**：
```http
GET /quota/usage?user_id={user_id}&date={date}
```

**响应示例**：
```json
{
  "code": 200,
  "data": {
    "user_id": "user_12345",
    "date": "2024-01-15",
    "usage": {
      "memory_operations": {
        "used": 45,
        "limit": 1000,
        "remaining": 955
      },
      "ai_conversations": {
        "used": 28,
        "limit": 500,
        "remaining": 472
      },
      "token_usage": {
        "used": 15420,
        "limit": 100000,
        "remaining": 84580
      }
    }
  }
}
```

## 9. 使用示例

### 9.1 完整对话流程示例

**场景**：用户"小明"与角色"小雪"进行对话

**步骤1：创建会话**
```bash
curl -X POST "https://game-api.yourcompany.com/v1/session/create" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_xiaoming",
    "character_id": "xiaoxue",
    "session_type": "conversation",
    "initial_context": {
      "scene": "classroom",
      "time_of_day": "afternoon"
    }
  }'
```

**步骤2：构建上下文**
```bash
curl -X POST "https://game-api.yourcompany.com/v1/context/build" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user_xiaoming" \
  -H "X-Character-ID: xiaoxue" \
  -d '{
    "current_message": "小雪，我今天考试考得不太好，有点沮丧。",
    "session_history": [],
    "max_context_tokens": 3000,
    "memory_recall_limit": 15,
    "include_relationship_context": true
  }'
```

**步骤3：生成AI回复**
```bash
curl -X POST "https://ark.cn-beijing.volces.com/api/v3/chat/completions" \
  -H "Authorization: Bearer your_volcano_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "ep-20241201000000-xxxxx",
    "messages": [
      {
        "role": "system",
        "content": "你是小雪，一个温柔善良的高中生。你和用户是好朋友，当前好感度：75分。"
      },
      {
        "role": "user",
        "content": "小雪，我今天考试考得不太好，有点沮丧。"
      }
    ],
    "max_tokens": 200,
    "temperature": 0.7
  }'
```

**步骤4：保存对话记忆**
```bash
curl -X POST "https://memory-api.bytedance.com/v1/memory/create" \
  -H "Authorization: Bearer your_memory_token" \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user_xiaoming" \
  -H "X-Character-ID: xiaoxue" \
  -d '{
    "memory_type": "conversation",
    "content": {
      "summary": "用户考试不理想感到沮丧，小雪给予安慰和鼓励",
      "key_points": ["考试失利", "情感支持", "友谊关怀"],
      "emotional_state": {
        "user_emotion": "disappointed",
        "character_emotion": "caring",
        "overall_mood": "supportive"
      },
      "context_tags": ["考试", "情感支持", "友谊"]
    },
    "importance_score": 0.8
  }'
```

### 9.2 批量记忆操作示例

```bash
curl -X POST "https://memory-api.bytedance.com/v1/memory/batch" \
  -H "Authorization: Bearer your_memory_token" \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user_xiaoming" \
  -H "X-Character-ID: xiaoxue" \
  -d '{
    "operations": [
      {
        "operation": "create",
        "memory": {
          "memory_type": "conversation",
          "content": {
            "summary": "讨论了学习方法",
            "key_points": ["学习技巧", "时间管理"]
          },
          "importance_score": 0.6
        }
      },
      {
        "operation": "update",
        "memory_id": "mem_20240115_001",
        "updates": {
          "last_accessed": "2024-01-15T16:00:00Z",
          "access_count": 3
        }
      }
    ]
  }'
```

## 10. 最佳实践

### 10.1 性能优化建议

1. **批量操作**：尽量使用批量接口减少API调用次数
2. **缓存策略**：在客户端缓存频繁访问的数据
3. **异步处理**：非关键路径操作使用异步处理
4. **连接复用**：使用HTTP连接池复用连接
5. **压缩传输**：启用gzip压缩减少传输数据量

### 10.2 错误处理建议

1. **重试机制**：对临时性错误实施指数退避重试
2. **降级策略**：关键服务不可用时启用降级模式
3. **监控告警**：设置关键指标监控和告警
4. **日志记录**：详细记录API调用日志便于排查问题
5. **超时设置**：合理设置请求超时时间

### 10.3 安全注意事项

1. **Token管理**：定期轮换API Token，避免泄露
2. **HTTPS传输**：所有API调用必须使用HTTPS
3. **参数验证**：客户端和服务端都要进行参数验证
4. **频率限制**：遵守API频率限制，避免被限流
5. **敏感数据**：不在日志中记录敏感信息

通过这份详细的API接口调用说明文档，开发者可以快速理解和使用多AI智能体游戏记忆库的各项功能，实现高效的角色记忆管理和智能对话处理。
```
```

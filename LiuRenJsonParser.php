<?php

class LiuRenJsonParser {
    
    // 地支顺序数组
    private static $DI_ZHI_ORDER = [
        "子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"
    ];
    
    // 天将名称映射
    private static $TIAN_JIANG_MAP = [
        "贵" => "贵人",
        "蛇" => "螣蛇",
        "朱" => "朱雀",
        "合" => "六合",
        "勾" => "勾陈",
        "青" => "青龙",
        "空" => "天空",
        "虎" => "白虎",
        "常" => "太常",
        "玄" => "玄武",
        "阴" => "太阴",
        "后" => "天后"
    ];

    // 六亲关系映射
    private static $LIU_QIN_MAP = [
        "父" => "父母",
        "财" => "妻财",
        "兄" => "兄弟",
        "子" => "子孙",
        "官" => "官鬼"
    ];
    
    public static function main() {
        $jsonStr = '{"nowTianPan":["寅","卯","辰","巳","午","未","申","酉","戌","亥","子","丑"],"siKe":[["己","酉"],["酉","亥"],["亥","丑"],["丑","卯"]],"sanChuan":["丑","卯","巳"],"guiRenArr":{"子":"贵","亥":"蛇","戌":"朱","酉":"合","申":"勾","未":"青","午":"空","巳":"虎","辰":"常","卯":"玄","寅":"阴","丑":"后"},"xunTianPan":{"寅":"壬","卯":"癸","辰":"◎","kongWang":["辰","巳"],"xianKong":["午","未"],"巳":"◎","午":"甲","未":"乙","申":"丙","酉":"丁","戌":"戊","亥":"己","子":"庚","丑":"辛"},"diZhiLiuQin":{"子":"财","丑":"兄","寅":"官","卯":"官","辰":"兄","巳":"父","午":"父","未":"兄","申":"子","酉":"子","戌":"兄","亥":"财"},"YueJiang":"午","dayXun":"甲午"}';
        
        $result = self::parseLiuRenJson($jsonStr);
        echo $result;
    }
    
    public static function parseLiuRenJson($jsonStr) {
        $json = json_decode($jsonStr, true);
        $sb = "";
        
        // 解析月将
        $yueJiang = $json["YueJiang"];
        $sb .= "月将：" . $yueJiang . "\n\n";
        
        // 解析天地盘
        $sb .= "天地盘：\n\n";
        $nowTianPan = $json["nowTianPan"];
        for ($i = 0; $i < count(self::$DI_ZHI_ORDER); $i++) {
            $diPan = self::$DI_ZHI_ORDER[$i];
            $tianPan = $nowTianPan[$i];
            $sb .= "- 地盘" . $diPan . "位上的天盘地支：" . $tianPan . "\n";
        }
        $sb .= "\n";
        
        // 解析四课
        $sb .= "四课：\n";
        $siKe = $json["siKe"];
        $keNames = ["第一课", "第二课", "第三课", "第四课"];
        for ($i = 0; $i < count($siKe); $i++) {
            $ke = $siKe[$i];
            $gan = $ke[0];
            $zhi = $ke[1];
            
            if ($i == 0) {
                $sb .= "- " . $keNames[$i] . "：" . $gan . "（日干）上见" . $zhi . "\n";
            } else if ($i == 1) {
                $sb .= "- " . $keNames[$i] . "：" . $gan . "（地支）上见" . $zhi . "（天盘地支）" . "\n";
            } else if ($i == 2) {
                $sb .= "- " . $keNames[$i] . "：" . $gan . "（日支）上见" . $zhi . "\n";
            } else {
                $sb .= "- " . $keNames[$i] . "：" . $gan . "（地支）上见" . $zhi . "（天盘地支）" . "\n";
            }
        }
        $sb .= "\n";
        
        // 解析三传
        $sb .= "三传：\n";
        $sanChuan = $json["sanChuan"];
        $chuanNames = ["初传", "中传", "末传"];
        for ($i = 0; $i < count($sanChuan); $i++) {
            $sb .= "- " . $chuanNames[$i] . "：" . $sanChuan[$i] . "\n";
        }
        $sb .= "\n";
        
        // 解析天将排列
        $sb .= "天将排列：\n";
        $guiRenArr = $json["guiRenArr"];
        foreach (self::$DI_ZHI_ORDER as $diZhi) {
            if (isset($guiRenArr[$diZhi])) {
                $tianJiangCode = $guiRenArr[$diZhi];
                $tianJiangName = isset(self::$TIAN_JIANG_MAP[$tianJiangCode]) ? 
                    self::$TIAN_JIANG_MAP[$tianJiangCode] : $tianJiangCode;
                $sb .= "- 天盘地支" . $diZhi . "配天将：" . $tianJiangName . "\n";
            }
        }
        $sb .= "\n";
        
        // 解析六亲关系
        $sb .= "六亲关系：\n";
        $diZhiLiuQin = $json["diZhiLiuQin"];
        foreach (self::$DI_ZHI_ORDER as $diZhi) {
            if (isset($diZhiLiuQin[$diZhi])) {
                $liuQinCode = $diZhiLiuQin[$diZhi];
                $liuQinName = isset(self::$LIU_QIN_MAP[$liuQinCode]) ? 
                    self::$LIU_QIN_MAP[$liuQinCode] : $liuQinCode;
                $sb .= "- " . $diZhi . "为日干的" . $liuQinName . "\n";
            }
        }
        $sb .= "\n";
        
        // 解析空亡和陷空
        $xunTianPan = $json["xunTianPan"];
        if (isset($xunTianPan["kongWang"])) {
            $kongWang = $xunTianPan["kongWang"];
            $sb .= "空亡：";
            for ($i = 0; $i < count($kongWang); $i++) {
                if ($i > 0) $sb .= "、";
                $sb .= $kongWang[$i];
            }
            $sb .= "\n\n";
        }
        
        if (isset($xunTianPan["xianKong"])) {
            $xianKong = $xunTianPan["xianKong"];
            $sb .= "陷空：";
            for ($i = 0; $i < count($xianKong); $i++) {
                if ($i > 0) $sb .= "、";
                $sb .= $xianKong[$i];
            }
            $sb .= "\n\n";
        }
        
        // 解析遁干
        $sb .= "遁干：";
        $dunGanList = [];
        foreach (self::$DI_ZHI_ORDER as $diZhi) {
            if (isset($xunTianPan[$diZhi]) && $xunTianPan[$diZhi] !== "◎") {
                $gan = $xunTianPan[$diZhi];
                $dunGanList[] = $gan . $diZhi;
            }
        }
        $sb .= implode("、", $dunGanList);
        
        return $sb;
    }
}

// 运行示例
LiuRenJsonParser::main();

?>

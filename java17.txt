import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.*;

public class LiuRenJsonParser {
    
    // 地支顺序数组，用于索引对应
    private static final String[] DI_ZHI_ORDER = {
        "子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"
    };
    
    // 天将名称映射
    private static final Map<String, String> TIAN_JIANG_MAP = Map.of(
        "贵", "贵人", "蛇", "螣蛇", "朱", "朱雀", "合", "六合",
        "勾", "勾陈", "青", "青龙", "空", "天空", "虎", "白虎",
        "常", "太常", "玄", "玄武", "阴", "太阴", "后", "天后"
    );
    
    // 六亲关系映射
    private static final Map<String, String> LIU_QIN_MAP = Map.of(
        "父", "父母", "财", "妻财", "兄", "兄弟", 
        "子", "子孙", "官", "官鬼"
    );
    
    public static void main(String[] args) {
        String jsonStr = "{\"nowTianPan\":[\"未\",\"申\",\"酉\",\"戌\",\"亥\",\"子\",\"丑\",\"寅\",\"卯\",\"辰\",\"巳\",\"午\"],\"siKe\":[[\"甲\",\"酉\"],[\"酉\",\"辰\"],[\"子\",\"未\"],[\"未\",\"寅\"]],\"sanChuan\":[\"寅\",\"酉\",\"辰\"],\"guiRenArr\":{\"丑\":\"贵\",\"子\":\"蛇\",\"亥\":\"朱\",\"戌\":\"合\",\"酉\":\"勾\",\"申\":\"青\",\"未\":\"空\",\"午\":\"虎\",\"巳\":\"常\",\"辰\":\"玄\",\"卯\":\"阴\",\"寅\":\"后\"},\"xunTianPan\":{\"未\":\"辛\",\"申\":\"壬\",\"酉\":\"癸\",\"戌\":\"◎\",\"kongWang\":[\"戌\",\"亥\"],\"xianKong\":[\"巳\",\"午\"],\"亥\":\"◎\",\"子\":\"甲\",\"丑\":\"乙\",\"寅\":\"丙\",\"卯\":\"丁\",\"辰\":\"戊\",\"巳\":\"己\",\"午\":\"庚\"},\"diZhiLiuQin\":{\"子\":\"父\",\"丑\":\"财\",\"寅\":\"兄\",\"卯\":\"兄\",\"辰\":\"财\",\"巳\":\"子\",\"午\":\"子\",\"未\":\"财\",\"申\":\"官\",\"酉\":\"官\",\"戌\":\"财\",\"亥\":\"父\"},\"YueJiang\":\"申\",\"dayXun\":\"甲子\"}";
        
        try {
            String result = parseLiuRenJson(jsonStr);
            System.out.println(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public static String parseLiuRenJson(String jsonStr) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonStr);
        
        StringBuilder sb = new StringBuilder();
        
        // 解析月将
        String yueJiang = root.get("YueJiang").asText();
        sb.append("月将：").append(yueJiang).append("\n\n");
        
        // 解析天地盘
        sb.append("天地盘：\n\n");
        JsonNode nowTianPan = root.get("nowTianPan");
        for (int i = 0; i < DI_ZHI_ORDER.length; i++) {
            String diPan = DI_ZHI_ORDER[i];
            String tianPan = nowTianPan.get(i).asText();
            sb.append("- 地盘").append(diPan).append("位上的天盘地支：").append(tianPan).append("\n");
        }
        sb.append("\n");
        
        // 解析四课
        sb.append("四课：\n");
        JsonNode siKe = root.get("siKe");
        String[] keNames = {"第一课", "第二课", "第三课", "第四课"};
        for (int i = 0; i < siKe.size(); i++) {
            JsonNode ke = siKe.get(i);
            String gan = ke.get(0).asText();
            String zhi = ke.get(1).asText();
            
            if (i == 0) {
                sb.append("- ").append(keNames[i]).append("：").append(gan).append("（日干）上见").append(zhi).append("\n");
            } else if (i == 1) {
                sb.append("- ").append(keNames[i]).append("：").append(gan).append("（地支）上见").append(zhi).append("（天盘地支）").append("\n");
            } else if (i == 2) {
                sb.append("- ").append(keNames[i]).append("：").append(gan).append("（日支）上见").append(zhi).append("\n");
            } else {
                sb.append("- ").append(keNames[i]).append("：").append(gan).append("（地支）上见").append(zhi).append("（天盘地支）").append("\n");
            }
        }
        sb.append("\n");
        
        // 解析三传
        sb.append("三传：\n");
        JsonNode sanChuan = root.get("sanChuan");
        String[] chuanNames = {"初传", "中传", "末传"};
        for (int i = 0; i < sanChuan.size(); i++) {
            sb.append("- ").append(chuanNames[i]).append("：").append(sanChuan.get(i).asText()).append("\n");
        }
        sb.append("\n");
        
        // 解析天将排列
        sb.append("天将排列：\n");
        JsonNode guiRenArr = root.get("guiRenArr");
        for (String diZhi : DI_ZHI_ORDER) {
            if (guiRenArr.has(diZhi)) {
                String tianJiangCode = guiRenArr.get(diZhi).asText();
                String tianJiangName = TIAN_JIANG_MAP.getOrDefault(tianJiangCode, tianJiangCode);
                sb.append("- 天盘地支").append(diZhi).append("配天将：").append(tianJiangName).append("\n");
            }
        }
        sb.append("\n");
        
        // 解析六亲关系
        sb.append("六亲关系：\n");
        JsonNode diZhiLiuQin = root.get("diZhiLiuQin");
        for (String diZhi : DI_ZHI_ORDER) {
            if (diZhiLiuQin.has(diZhi)) {
                String liuQinCode = diZhiLiuQin.get(diZhi).asText();
                String liuQinName = LIU_QIN_MAP.getOrDefault(liuQinCode, liuQinCode);
                sb.append("- ").append(diZhi).append("为日干的").append(liuQinName).append("\n");
            }
        }
        sb.append("\n");
        
        // 解析空亡和陷空
        JsonNode xunTianPan = root.get("xunTianPan");
        if (xunTianPan.has("kongWang")) {
            JsonNode kongWang = xunTianPan.get("kongWang");
            sb.append("空亡：");
            for (int i = 0; i < kongWang.size(); i++) {
                if (i > 0) sb.append("、");
                sb.append(kongWang.get(i).asText());
            }
            sb.append("\n\n");
        }
        
        if (xunTianPan.has("xianKong")) {
            JsonNode xianKong = xunTianPan.get("xianKong");
            sb.append("陷空：");
            for (int i = 0; i < xianKong.size(); i++) {
                if (i > 0) sb.append("、");
                sb.append(xianKong.get(i).asText());
            }
            sb.append("\n\n");
        }
        
        // 解析遁干
        sb.append("遁干：");
        List<String> dunGanList = new ArrayList<>();
        for (String diZhi : DI_ZHI_ORDER) {
            if (xunTianPan.has(diZhi) && !xunTianPan.get(diZhi).asText().equals("◎")) {
                String gan = xunTianPan.get(diZhi).asText();
                dunGanList.add(gan + diZhi);
            }
        }
        sb.append(String.join("、", dunGanList));
        
        return sb.toString();
    }
}
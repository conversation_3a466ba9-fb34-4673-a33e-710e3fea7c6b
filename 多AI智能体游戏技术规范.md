# 多AI智能体游戏技术规范

## 1. 系统架构技术规范

### 1.1 整体架构设计

```
┌─────────────────────────────────────────────────────────┐
│                    客户端层 (Ren'Py)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │  游戏界面   │ │  角色管理   │ │    付费商城         │ │
│  │   模块      │ │   模块      │ │     模块            │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                            │ HTTPS + 签名验证
┌─────────────────────────────────────────────────────────┐
│                   API网关层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │  认证鉴权   │ │  请求限流   │ │    签名验证         │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                   业务服务层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │ AI对话服务  │ │ 图片生成    │ │   支付服务          │ │
│  │             │ │ 服务        │ │                     │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │ Token管理   │ │ 剧情回顾    │ │   用户管理          │ │
│  │ 服务        │ │ 服务(MCP)   │ │   服务              │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                   数据存储层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │ PostgreSQL  │ │    Redis    │ │   对象存储          │ │
│  │(核心数据)   │ │(缓存/会话)  │ │  (图片/音频)        │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 1.2 安全架构规范

**分层安全策略：**

- **L1 - 基础保护层**：RPA文件打包加密、基础代码混淆、客户端完整性检查
- **L2 - 网络安全层**：HTTPS强制传输、请求签名验证、API密钥动态轮换
- **L3 - 业务逻辑层**：核心逻辑服务端化、用户权限验证、付费状态验证
- **L4 - 数据保护层**：敏感数据加密存储、用户隐私数据脱敏、审计日志记录

## 2. 核心模块技术规范

### 2.1 AI对话服务规范

**服务接口定义：**
```
POST /api/v1/ai/chat
Headers:
  Authorization: Bearer {jwt_token}
  X-Signature: {request_signature}
  X-Timestamp: {unix_timestamp}

Request Body:
{
  "session_id": "string",
  "character_id": "string", 
  "user_message": "string",
  "context_mode": "full|summary|minimal",
  "max_tokens": integer
}

Response:
{
  "success": boolean,
  "data": {
    "ai_response": "string",
    "character_name": "string",
    "token_usage": {
      "prompt_tokens": integer,
      "completion_tokens": integer,
      "total_tokens": integer
    },
    "context_status": {
      "current_tokens": integer,
      "max_tokens": integer,
      "needs_cleanup": boolean
    }
  },
  "error": "string"
}
```

**Token管理策略：**
- **硬限制**：单次对话最大8000 tokens
- **软限制**：达到6000 tokens时触发清理提醒
- **清理策略**：保留最近20轮对话 + 重要剧情节点
- **压缩策略**：历史对话智能摘要压缩

### 2.2 图片生成与管理规范

**自定义角色图片系统：**

**图片生成接口：**
```
POST /api/v1/image/generate
Headers:
  Authorization: Bearer {jwt_token}
  X-Signature: {request_signature}

Request Body:
{
  "character_id": "string",
  "prompt": "string",
  "style": "anime|realistic|cartoon",
  "size": "512x512|1024x1024",
  "payment_token": "string"
}

Response:
{
  "success": boolean,
  "data": {
    "image_id": "string",
    "image_url": "string",
    "thumbnail_url": "string",
    "generation_cost": number,
    "expires_at": "datetime"
  }
}
```

**付费策略规范：**
- **基础生成**：免费用户每日3次
- **高级生成**：付费用户无限制
- **定制生成**：特殊风格需要付费解锁
- **商用授权**：额外付费获得商用权限

**图片存储规范：**
- **临时存储**：生成后24小时自动删除
- **永久存储**：付费后转为永久存储
- **CDN分发**：全球加速访问
- **版权保护**：水印和使用追踪

### 2.3 支付系统规范

**支付商品定义：**
```json
{
  "products": [
    {
      "id": "premium_monthly",
      "name": "高级会员(月)",
      "price": 19.9,
      "currency": "CNY",
      "benefits": [
        "无限AI对话",
        "无限图片生成", 
        "专属角色包",
        "优先客服支持"
      ]
    },
    {
      "id": "character_pack_1",
      "name": "角色包-校园系列",
      "price": 9.9,
      "currency": "CNY",
      "type": "one_time",
      "content": ["角色A", "角色B", "角色C"]
    },
    {
      "id": "image_credits_100",
      "name": "图片生成积分100点",
      "price": 29.9,
      "currency": "CNY",
      "credits": 100
    }
  ]
}
```

**支付流程规范：**
1. **订单创建** → 服务端生成订单号
2. **支付调起** → 调用第三方支付接口
3. **支付验证** → 回调验证支付结果
4. **权益发放** → 自动发放购买内容
5. **订单完成** → 记录交易日志

### 2.4 Token管理与剧情回顾规范

**Token清理策略：**

**智能压缩算法：**
```
Token清理优先级：
1. 保留：最近5轮对话（完整保留）
2. 压缩：6-20轮对话（摘要保存）
3. 存档：重要剧情节点（永久保存）
4. 删除：普通闲聊内容（超过阈值删除）
```

**MCP剧情回顾系统：**

**MCP服务接口规范：**
```
MCP Server: story-review-service
Capabilities:
- tools/story_search
- tools/story_summary  
- tools/context_rebuild
- resources/dialogue_history
- resources/character_memory

Tool: story_search
Input:
{
  "query": "string",
  "character_filter": ["string"],
  "time_range": {
    "start": "datetime",
    "end": "datetime"
  },
  "max_results": integer
}

Output:
{
  "results": [
    {
      "dialogue_id": "string",
      "timestamp": "datetime",
      "characters": ["string"],
      "content": "string",
      "importance_score": number
    }
  ]
}
```

**剧情回顾功能：**
- **关键词搜索**：搜索历史对话中的关键信息
- **角色记忆**：查看特定角色的互动历史
- **情节摘要**：自动生成剧情发展摘要
- **上下文重建**：基于历史数据重建对话上下文

## 3. 数据库设计规范

### 3.1 核心表结构

**用户表 (users)**
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  subscription_type VARCHAR(20) DEFAULT 'free',
  subscription_expires_at TIMESTAMP,
  image_credits INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**对话会话表 (chat_sessions)**
```sql
CREATE TABLE chat_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  session_name VARCHAR(100),
  current_tokens INTEGER DEFAULT 0,
  max_tokens INTEGER DEFAULT 8000,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**对话历史表 (dialogue_history)**
```sql
CREATE TABLE dialogue_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES chat_sessions(id),
  character_id UUID,
  speaker_type VARCHAR(20) NOT NULL, -- 'user', 'ai', 'system'
  speaker_name VARCHAR(50),
  content TEXT NOT NULL,
  token_count INTEGER,
  importance_score FLOAT DEFAULT 0.5,
  is_compressed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**角色图片表 (character_images)**
```sql
CREATE TABLE character_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  character_id UUID,
  image_url VARCHAR(500),
  thumbnail_url VARCHAR(500),
  generation_prompt TEXT,
  is_paid BOOLEAN DEFAULT FALSE,
  is_permanent BOOLEAN DEFAULT FALSE,
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**支付订单表 (payment_orders)**
```sql
CREATE TABLE payment_orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  product_id VARCHAR(50) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'CNY',
  status VARCHAR(20) DEFAULT 'pending',
  payment_method VARCHAR(50),
  third_party_order_id VARCHAR(100),
  paid_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.2 索引优化规范

**性能索引：**
```sql
-- 对话历史查询优化
CREATE INDEX idx_dialogue_session_time ON dialogue_history(session_id, created_at DESC);
CREATE INDEX idx_dialogue_character ON dialogue_history(character_id, created_at DESC);
CREATE INDEX idx_dialogue_importance ON dialogue_history(importance_score DESC, created_at DESC);

-- 用户查询优化
CREATE INDEX idx_users_subscription ON users(subscription_type, subscription_expires_at);
CREATE INDEX idx_users_email ON users(email);

-- 支付订单优化
CREATE INDEX idx_orders_user_status ON payment_orders(user_id, status, created_at DESC);
```

## 4. API安全规范

### 4.1 认证授权规范

**JWT Token结构：**
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "user_id": "uuid",
    "username": "string",
    "subscription": "free|premium",
    "permissions": ["chat", "image_gen", "premium_features"],
    "exp": 1234567890,
    "iat": 1234567890
  }
}
```

**请求签名算法：**
```
签名字符串构建：
HTTP_METHOD + "\n" +
REQUEST_PATH + "\n" + 
QUERY_STRING + "\n" +
REQUEST_BODY + "\n" +
TIMESTAMP

签名计算：
HMAC-SHA256(签名字符串, SECRET_KEY)
```

### 4.2 限流规范

**API限流策略：**
```yaml
rate_limits:
  free_user:
    ai_chat: "10/minute, 100/hour, 500/day"
    image_gen: "3/day"
    story_search: "20/hour"

  premium_user:
    ai_chat: "60/minute, 1000/hour"
    image_gen: "unlimited"
    story_search: "unlimited"

  global:
    login: "5/minute per IP"
    register: "3/hour per IP"
```

## 5. 部署与运维规范

### 5.1 容器化部署

**Docker Compose配置：**
```yaml
version: '3.8'
services:
  api-gateway:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl

  game-api:
    build: ./api
    environment:
      - DATABASE_URL=******************************/gamedb
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=gamedb
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
```

### 5.2 监控告警规范

**关键指标监控：**
- **API响应时间**：P95 < 500ms
- **错误率**：< 1%
- **Token使用量**：实时监控成本
- **支付成功率**：> 99%
- **用户活跃度**：DAU/MAU趋势

**告警规则：**
```yaml
alerts:
  - name: "API响应时间过长"
    condition: "avg(response_time) > 1000ms for 5m"
    severity: "warning"

  - name: "支付失败率过高"
    condition: "payment_failure_rate > 5% for 10m"
    severity: "critical"

  - name: "Token成本异常"
    condition: "hourly_token_cost > threshold * 1.5"
    severity: "warning"
```

## 6. 开发流程规范

### 6.1 代码规范

**Python代码规范：**
- 遵循PEP 8标准
- 使用类型注解
- 100%单元测试覆盖率
- 代码审查必须通过

**Ren'Py脚本规范：**
- 统一的命名约定
- 模块化组织结构
- 注释覆盖率 > 80%
- 敏感信息环境变量化

### 6.2 测试规范

**测试分层：**
- **单元测试**：覆盖所有业务逻辑
- **集成测试**：API接口完整性测试
- **端到端测试**：用户完整流程测试
- **性能测试**：负载和压力测试

**测试环境：**
- **开发环境**：本地开发测试
- **测试环境**：功能验证测试
- **预发环境**：生产前最终测试
- **生产环境**：线上监控测试

## 7. 安全合规规范

### 7.1 数据保护规范

**用户隐私保护：**
- 对话数据加密存储
- 个人信息脱敏处理
- 数据访问权限控制
- 数据删除和导出功能

**GDPR合规：**
- 用户同意机制
- 数据处理透明度
- 用户权利保障
- 数据泄露通知机制

### 7.2 内容安全规范

**AI内容审核：**
- 敏感词过滤
- 不当内容检测
- 用户举报机制
- 人工审核流程

**版权保护：**
- 生成内容水印
- 使用追踪机制
- 侵权检测系统
- 法律维权流程

## 8. 实施路线图

### 8.1 第一阶段：基础架构（4-6周）

**核心功能开发：**
1. **用户认证系统**：JWT认证、用户注册登录
2. **AI对话服务**：火山引擎API集成、基础对话功能
3. **数据库设计**：核心表结构创建、基础数据迁移
4. **API网关**：请求路由、基础安全验证

**安全措施实施：**
1. **HTTPS配置**：SSL证书部署、强制HTTPS
2. **请求签名**：基础签名验证机制
3. **基础限流**：API调用频率限制
4. **日志系统**：访问日志、错误日志记录

### 8.2 第二阶段：核心功能（6-8周）

**业务功能开发：**
1. **Token管理系统**：智能压缩、清理策略
2. **图片生成服务**：AI图片生成、存储管理
3. **支付系统**：订单管理、第三方支付集成
4. **MCP剧情回顾**：历史搜索、上下文重建

**客户端开发：**
1. **Ren'Py游戏框架**：基础游戏界面、角色系统
2. **网络通信模块**：API调用、错误处理
3. **用户界面优化**：付费商城、角色管理
4. **本地数据管理**：存档系统、缓存机制

### 8.3 第三阶段：高级功能（4-6周）

**高级特性开发：**
1. **智能对话优化**：上下文管理、个性化推荐
2. **高级图片功能**：风格定制、批量生成
3. **会员系统**：订阅管理、权益分发
4. **数据分析**：用户行为分析、运营指标

**安全加固：**
1. **高级保护措施**：代码混淆、反调试
2. **内容安全**：敏感内容过滤、审核机制
3. **隐私保护**：数据加密、GDPR合规
4. **监控告警**：实时监控、异常告警

### 8.4 第四阶段：优化上线（2-4周）

**性能优化：**
1. **数据库优化**：索引优化、查询性能调优
2. **缓存策略**：Redis缓存、CDN配置
3. **负载均衡**：多实例部署、流量分发
4. **监控完善**：性能监控、业务监控

**上线准备：**
1. **压力测试**：负载测试、稳定性测试
2. **安全测试**：渗透测试、安全扫描
3. **用户测试**：Beta测试、用户反馈收集
4. **运维准备**：部署脚本、应急预案

## 9. 成本预算规划

### 9.1 开发成本

**人力成本：**
- **后端开发**：2人 × 4个月 = 8人月
- **前端/游戏开发**：1人 × 4个月 = 4人月
- **UI/UX设计**：1人 × 2个月 = 2人月
- **测试工程师**：1人 × 2个月 = 2人月
- **项目管理**：0.5人 × 4个月 = 2人月

**总计**：18人月（按平均月薪15K计算，约27万元）

### 9.2 运营成本（月度）

**基础设施成本：**
- **云服务器**：2000元/月（4核8G × 2台）
- **数据库**：1000元/月（PostgreSQL + Redis）
- **对象存储**：500元/月（图片、音频存储）
- **CDN**：800元/月（全球加速）

**AI服务成本：**
- **火山引擎API**：5000-15000元/月（根据用户量）
- **图片生成API**：2000-8000元/月（根据使用量）

**第三方服务：**
- **支付通道**：交易额的2-3%
- **短信服务**：200元/月
- **监控服务**：300元/月

**总计**：10000-28000元/月（不含支付手续费）

### 9.3 收入预期

**付费模式：**
- **会员订阅**：19.9元/月，预期转化率5-10%
- **角色包**：9.9元/个，预期购买率20-30%
- **图片生成积分**：29.9元/100点，预期使用率15-25%

**收入预测（月活1万用户）：**
- **会员收入**：500用户 × 19.9元 = 9950元
- **角色包收入**：2000用户 × 9.9元 = 19800元
- **积分收入**：1500用户 × 29.9元 = 44850元

**月收入预期**：74600元（理想情况下）

## 10. 风险评估与应对

### 10.1 技术风险

**主要风险：**
1. **AI API成本失控**：用户量激增导致成本暴涨
2. **性能瓶颈**：高并发下系统响应缓慢
3. **安全漏洞**：用户数据泄露或系统被攻击
4. **第三方依赖**：火山引擎等服务不稳定

**应对措施：**
1. **成本控制**：实时监控、智能限流、预算告警
2. **性能优化**：缓存策略、数据库优化、负载均衡
3. **安全加固**：定期安全审计、漏洞扫描、应急响应
4. **多重备份**：多个AI服务商、降级方案、容灾备份

### 10.2 商业风险

**主要风险：**
1. **市场竞争**：大厂推出类似产品
2. **用户留存**：新鲜感过后用户流失
3. **内容监管**：AI生成内容合规问题
4. **版权纠纷**：生成内容侵权风险

**应对措施：**
1. **差异化竞争**：独特的角色设定、优质的用户体验
2. **用户运营**：持续内容更新、社区建设、用户反馈
3. **合规建设**：内容审核机制、法律咨询、行业标准
4. **版权保护**：原创内容、使用协议、法律维权
